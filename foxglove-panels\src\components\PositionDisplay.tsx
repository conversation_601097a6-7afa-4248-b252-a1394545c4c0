import React, { useState, useEffect } from 'react';
import '../styles/PositionDisplay.css';

interface PositionDisplayProps {
  position: { x: number; y: number; z: number };
  orientation?: { x: number; y: number; z: number; w: number };
  velocity?: { linear: { x: number; y: number; z: number }; angular: { x: number; y: number; z: number } };
  dataPointsCount: number;
  isConnected: boolean;
  timestamp?: string;
}

const PositionDisplay: React.FC<PositionDisplayProps> = ({
  position,
  orientation,
  velocity,
  dataPointsCount,
  isConnected,
  timestamp
}) => {
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    setLastUpdate(new Date());
    setIsUpdating(true);
    const timer = setTimeout(() => setIsUpdating(false), 300);
    return () => clearTimeout(timer);
  }, [position, orientation, velocity]);

  // Convert quaternion to Euler angles for display
  const getEulerFromQuaternion = (q: { x: number; y: number; z: number; w: number }) => {
    const { x, y, z, w } = q;
    const roll = Math.atan2(2 * (w * x + y * z), 1 - 2 * (x * x + y * y));
    const pitch = Math.asin(2 * (w * y - z * x));
    const yaw = Math.atan2(2 * (w * z + x * y), 1 - 2 * (y * y + z * z));
    return { roll, pitch, yaw };
  };

  const euler = orientation ? getEulerFromQuaternion(orientation) : null;

  return (
    <div className="position-display-container">
      <div className="position-display-header">
        <h3 className="position-display-title">
          <span className="position-display-icon">📍</span>
          Robot Position
        </h3>
      </div>

      <div className="position-data-container">
        {/* Position Section */}
        <div className="position-section">
          <div className="position-section-title">Position (m)</div>
          <div className="position-data-grid">
            <div className={`position-data-item position-coordinate ${isUpdating ? 'updated' : ''}`}>
              <div className="position-data-label">X</div>
              <div className="position-data-value">{position.x.toFixed(3)}</div>
            </div>
            <div className={`position-data-item position-coordinate ${isUpdating ? 'updated' : ''}`}>
              <div className="position-data-label">Y</div>
              <div className="position-data-value">{position.y.toFixed(3)}</div>
            </div>
            <div className={`position-data-item position-coordinate ${isUpdating ? 'updated' : ''}`}>
              <div className="position-data-label">Z</div>
              <div className="position-data-value">{position.z.toFixed(3)}</div>
            </div>
          </div>
        </div>

        {/* Orientation Section */}
        {euler && (
          <div className="position-section">
            <div className="position-section-title">Orientation (rad)</div>
            <div className="position-data-grid">
              <div className={`position-data-item position-orientation ${isUpdating ? 'updated' : ''}`}>
                <div className="position-data-label">Roll</div>
                <div className="position-data-value">{euler.roll.toFixed(3)}</div>
              </div>
              <div className={`position-data-item position-orientation ${isUpdating ? 'updated' : ''}`}>
                <div className="position-data-label">Pitch</div>
                <div className="position-data-value">{euler.pitch.toFixed(3)}</div>
              </div>
              <div className={`position-data-item position-orientation ${isUpdating ? 'updated' : ''}`}>
                <div className="position-data-label">Yaw</div>
                <div className="position-data-value">{euler.yaw.toFixed(3)}</div>
              </div>
            </div>
          </div>
        )}

        {/* Velocity Section */}
        {velocity && (
          <div className="position-section">
            <div className="position-section-title">Velocity (m/s, rad/s)</div>
            <div className="position-data-grid">
              <div className={`position-data-item position-velocity ${isUpdating ? 'updated' : ''}`}>
                <div className="position-data-label">Linear X</div>
                <div className="position-data-value">{velocity.linear.x.toFixed(3)}</div>
              </div>
              <div className={`position-data-item position-velocity ${isUpdating ? 'updated' : ''}`}>
                <div className="position-data-label">Linear Y</div>
                <div className="position-data-value">{velocity.linear.y.toFixed(3)}</div>
              </div>
              <div className={`position-data-item position-velocity ${isUpdating ? 'updated' : ''}`}>
                <div className="position-data-label">Angular Z</div>
                <div className="position-data-value">{velocity.angular.z.toFixed(3)}</div>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="position-status">
        <div className="position-timestamp">
          Last update: {lastUpdate.toLocaleTimeString()}
          {timestamp && ` | ROS time: ${timestamp}`}
          <br />
          Data points: {dataPointsCount}
        </div>
        <div className={`position-connection-status ${isConnected ? 'position-connected' : 'position-disconnected'}`}>
          {isConnected ? 'Connected' : 'Disconnected'}
        </div>
      </div>
    </div>
  );
};

export default PositionDisplay;
