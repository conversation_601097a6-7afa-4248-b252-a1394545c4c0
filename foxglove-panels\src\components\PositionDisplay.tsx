import React from 'react';

interface PositionDisplayProps {
  position: { x: number; y: number; z: number };
  dataPointsCount: number;
}

const PositionDisplay: React.FC<PositionDisplayProps> = ({ position, dataPointsCount }) => {
  return (
    <div style={{
      backgroundColor: '#1a1a1a',
      border: '1px solid #333',
      borderRadius: '8px',
      padding: '20px'
    }}>
      <h3 style={{ color: '#00bcd4', marginTop: 0 }}>📈 Position Data (/odom)</h3>
      <div style={{
        backgroundColor: '#000',
        borderRadius: '4px',
        border: '1px solid #444',
        padding: '15px'
      }}>
        <div style={{ marginBottom: '10px' }}>
          <strong style={{ color: '#00bcd4' }}>Current Position:</strong>
        </div>
        <div style={{ fontSize: '14px', color: '#aaa', marginBottom: '8px' }}>
          X: {position.x.toFixed(3)}m
        </div>
        <div style={{ fontSize: '14px', color: '#aaa', marginBottom: '8px' }}>
          Y: {position.y.toFixed(3)}m
        </div>
        <div style={{ fontSize: '14px', color: '#aaa' }}>
          Z: {position.z.toFixed(3)}m
        </div>
      </div>
      <div style={{ fontSize: '12px', color: '#aaa', marginTop: '10px' }}>
        Data points received: {dataPointsCount}
      </div>
    </div>
  );
};

export default PositionDisplay;
