/* Connection Status Component Styles */
.connection-status-container {
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 15px 20px;
  margin-bottom: 20px;
}

.connection-status-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.connection-status-title {
  color: #00BCD4;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.connection-status-icon {
  margin-right: 8px;
  font-size: 18px;
}

.connection-toggle-button {
  background-color: #333;
  border: 1px solid #555;
  color: #fff;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.connection-toggle-button:hover {
  background-color: #444;
}

.connection-toggle-button.connected {
  background-color: #F44336;
  border-color: #F44336;
}

.connection-toggle-button.disconnected {
  background-color: #4CAF50;
  border-color: #4CAF50;
}

.connection-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.connection-detail-item {
  background-color: #000;
  border: 1px solid #444;
  border-radius: 4px;
  padding: 12px;
}

.connection-detail-label {
  color: #aaa;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 6px;
}

.connection-detail-value {
  color: #fff;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  font-weight: 600;
  word-break: break-all;
}

.connection-url {
  color: #00BCD4;
}

.connection-status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-connected {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid #4CAF50;
}

.status-connecting {
  background-color: rgba(255, 152, 0, 0.2);
  color: #FF9800;
  border: 1px solid #FF9800;
  animation: statusPulse 1.5s infinite;
}

.status-disconnected {
  background-color: rgba(244, 67, 54, 0.2);
  color: #F44336;
  border: 1px solid #F44336;
}

.status-error {
  background-color: rgba(156, 39, 176, 0.2);
  color: #9C27B0;
  border: 1px solid #9C27B0;
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

/* Connection Statistics */
.connection-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #333;
}

.connection-stat {
  text-align: center;
}

.connection-stat-value {
  color: #fff;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
}

.connection-stat-label {
  color: #aaa;
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-uptime {
  color: #4CAF50;
}

.stat-messages {
  color: #2196F3;
}

.stat-errors {
  color: #F44336;
}

/* Connection Health Indicator */
.connection-health {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.connection-health-label {
  color: #aaa;
  font-size: 11px;
  margin-right: 10px;
}

.connection-health-bar {
  flex: 1;
  height: 4px;
  background-color: #333;
  border-radius: 2px;
  overflow: hidden;
}

.connection-health-fill {
  height: 100%;
  transition: width 0.3s ease, background-color 0.3s ease;
  border-radius: 2px;
}

.health-excellent {
  background-color: #4CAF50;
}

.health-good {
  background-color: #8BC34A;
}

.health-fair {
  background-color: #FF9800;
}

.health-poor {
  background-color: #F44336;
}

/* Responsive Design */
@media (max-width: 768px) {
  .connection-status-container {
    padding: 12px 15px;
  }
  
  .connection-status-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .connection-details {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .connection-detail-item {
    padding: 10px;
  }
  
  .connection-stats {
    flex-direction: column;
    gap: 10px;
  }
  
  .connection-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .connection-stat-label {
    margin-bottom: 0;
  }
}

/* Animation for connection state changes */
.connection-state-change {
  animation: connectionStateChange 0.5s ease-in-out;
}

@keyframes connectionStateChange {
  0% { 
    transform: scale(1);
    opacity: 1;
  }
  50% { 
    transform: scale(1.02);
    opacity: 0.8;
  }
  100% { 
    transform: scale(1);
    opacity: 1;
  }
}
