import React, { useRef, useEffect, useState, useCallback } from 'react';
import * as THREE from 'three';
import '../styles/ThreeVisualization.css';

interface ThreeVisualizationProps {
  websocketUrl: string;
  topics: string[];
  width?: number;
  height?: number;
}

interface RobotData {
  position: { x: number; y: number; z: number };
  orientation: { x: number; y: number; z: number; w: number };
  laserScan?: {
    ranges: number[];
    angle_min: number;
    angle_max: number;
    angle_increment: number;
  };
  map?: {
    data: number[];
    width: number;
    height: number;
    resolution: number;
    origin: { x: number; y: number; z: number };
  };
}

export const ThreeVisualization: React.FC<ThreeVisualizationProps> = ({
  websocketUrl,
  topics,
  width = 800,
  height = 400
}) => {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const robotMeshRef = useRef<THREE.Mesh | null>(null);
  const laserPointsRef = useRef<THREE.Points | null>(null);
  const mapMeshRef = useRef<THREE.Mesh | null>(null);
  const animationIdRef = useRef<number | null>(null);
  
  const [isConnected, setIsConnected] = useState(false);
  const [robotData, setRobotData] = useState<RobotData>({
    position: { x: 0, y: 0, z: 0 },
    orientation: { x: 0, y: 0, z: 0, w: 1 }
  });
  const [renderStats, setRenderStats] = useState({
    fps: 0,
    triangles: 0,
    objects: 0
  });
  const [cameraPosition, setCameraPosition] = useState({ x: 5, y: 5, z: 5 });
  const [showGrid, setShowGrid] = useState(true);
  const [showAxes, setShowAxes] = useState(true);

  // Initialize Three.js scene
  const initThreeJS = useCallback(() => {
    if (!mountRef.current) return;

    // Scene
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x0a0a0a);
    sceneRef.current = scene;

    // Camera
    const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
    camera.position.set(5, 5, 5);
    camera.lookAt(0, 0, 0);
    cameraRef.current = camera;

    // Renderer
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(width, height);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    rendererRef.current = renderer;

    // Add grid
    if (showGrid) {
      const gridHelper = new THREE.GridHelper(20, 20, 0x444444, 0x222222);
      scene.add(gridHelper);
    }

    // Add axes helper
    if (showAxes) {
      const axesHelper = new THREE.AxesHelper(2);
      scene.add(axesHelper);
    }

    // Add lighting
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    scene.add(directionalLight);

    // Create robot representation
    const robotGeometry = new THREE.ConeGeometry(0.2, 0.5, 8);
    const robotMaterial = new THREE.MeshLambertMaterial({ color: 0xe91e63 });
    const robotMesh = new THREE.Mesh(robotGeometry, robotMaterial);
    robotMesh.castShadow = true;
    scene.add(robotMesh);
    robotMeshRef.current = robotMesh;

    // Mount renderer
    mountRef.current.appendChild(renderer.domElement);

    // Start render loop
    const animate = () => {
      animationIdRef.current = requestAnimationFrame(animate);
      
      if (rendererRef.current && sceneRef.current && cameraRef.current) {
        rendererRef.current.render(sceneRef.current, cameraRef.current);
        
        // Update stats
        setRenderStats({
          fps: Math.round(1000 / 16), // Approximate FPS
          triangles: rendererRef.current.info.render.triangles,
          objects: sceneRef.current.children.length
        });
      }
    };
    animate();

  }, [width, height, showGrid, showAxes]);

  // Update robot position and orientation
  const updateRobotPose = useCallback((data: RobotData) => {
    if (!robotMeshRef.current) return;

    const { position, orientation } = data;
    
    // Update position
    robotMeshRef.current.position.set(position.x, position.z, -position.y);
    
    // Update orientation (convert quaternion to Euler)
    const quaternion = new THREE.Quaternion(
      orientation.x,
      orientation.z,
      -orientation.y,
      orientation.w
    );
    robotMeshRef.current.setRotationFromQuaternion(quaternion);
  }, []);

  // Update laser scan visualization
  const updateLaserScan = useCallback((laserData: RobotData['laserScan']) => {
    if (!laserData || !sceneRef.current) return;

    // Remove existing laser points
    if (laserPointsRef.current) {
      sceneRef.current.remove(laserPointsRef.current);
    }

    const { ranges, angle_min, angle_increment } = laserData;
    const points: THREE.Vector3[] = [];
    const colors: number[] = [];

    ranges.forEach((range, index) => {
      if (range > 0 && range < 30) { // Valid range
        const angle = angle_min + index * angle_increment;
        const x = range * Math.cos(angle);
        const y = range * Math.sin(angle);
        
        points.push(new THREE.Vector3(x, 0.1, -y));
        
        // Color based on distance
        const normalizedRange = Math.min(range / 10, 1);
        colors.push(1 - normalizedRange, normalizedRange, 0); // Red to green
      }
    });

    if (points.length > 0) {
      const geometry = new THREE.BufferGeometry().setFromPoints(points);
      geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
      
      const material = new THREE.PointsMaterial({
        size: 0.05,
        vertexColors: true,
        transparent: true,
        opacity: 0.8
      });
      
      const laserPoints = new THREE.Points(geometry, material);
      sceneRef.current.add(laserPoints);
      laserPointsRef.current = laserPoints;
    }
  }, []);

  // Update map visualization
  const updateMap = useCallback((mapData: RobotData['map']) => {
    if (!mapData || !sceneRef.current) return;

    // Remove existing map
    if (mapMeshRef.current) {
      sceneRef.current.remove(mapMeshRef.current);
    }

    const { data, width: mapWidth, height: mapHeight, resolution, origin } = mapData;
    
    // Create map texture
    const canvas = document.createElement('canvas');
    canvas.width = mapWidth;
    canvas.height = mapHeight;
    const ctx = canvas.getContext('2d');
    
    if (ctx) {
      const imageData = ctx.createImageData(mapWidth, mapHeight);
      
      for (let i = 0; i < data.length; i++) {
        const value = data[i];
        const pixelIndex = i * 4;
        
        if (value === -1) {
          // Unknown space - gray
          imageData.data[pixelIndex] = 128;
          imageData.data[pixelIndex + 1] = 128;
          imageData.data[pixelIndex + 2] = 128;
        } else if (value === 0) {
          // Free space - white
          imageData.data[pixelIndex] = 255;
          imageData.data[pixelIndex + 1] = 255;
          imageData.data[pixelIndex + 2] = 255;
        } else {
          // Occupied space - black
          imageData.data[pixelIndex] = 0;
          imageData.data[pixelIndex + 1] = 0;
          imageData.data[pixelIndex + 2] = 0;
        }
        imageData.data[pixelIndex + 3] = 255; // Alpha
      }
      
      ctx.putImageData(imageData, 0, 0);
      
      // Create Three.js texture and mesh
      const texture = new THREE.CanvasTexture(canvas);
      texture.flipY = false;
      
      const geometry = new THREE.PlaneGeometry(
        mapWidth * resolution,
        mapHeight * resolution
      );
      const material = new THREE.MeshBasicMaterial({ 
        map: texture,
        transparent: true,
        opacity: 0.8
      });
      
      const mapMesh = new THREE.Mesh(geometry, material);
      mapMesh.rotation.x = -Math.PI / 2; // Lay flat
      mapMesh.position.set(
        origin.x + (mapWidth * resolution) / 2,
        0,
        -(origin.y + (mapHeight * resolution) / 2)
      );
      
      sceneRef.current.add(mapMesh);
      mapMeshRef.current = mapMesh;
    }
  }, []);

  // WebSocket connection and data handling
  useEffect(() => {
    const ws = new WebSocket(websocketUrl);
    
    ws.onopen = () => {
      setIsConnected(true);
      
      // Subscribe to topics
      topics.forEach(topic => {
        ws.send(JSON.stringify({
          op: 'subscribe',
          topic: topic,
          type: getTopicType(topic)
        }));
      });
    };

    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        
        if (message.topic === '/odom' && message.msg) {
          const pose = message.msg.pose.pose;
          setRobotData(prev => ({
            ...prev,
            position: pose.position,
            orientation: pose.orientation
          }));
          updateRobotPose({
            position: pose.position,
            orientation: pose.orientation
          });
        }
        
        if (message.topic === '/scan' && message.msg) {
          const laserData = {
            ranges: message.msg.ranges,
            angle_min: message.msg.angle_min,
            angle_max: message.msg.angle_max,
            angle_increment: message.msg.angle_increment
          };
          setRobotData(prev => ({ ...prev, laserScan: laserData }));
          updateLaserScan(laserData);
        }
        
        if (message.topic === '/map' && message.msg) {
          const mapData = {
            data: message.msg.data,
            width: message.msg.info.width,
            height: message.msg.info.height,
            resolution: message.msg.info.resolution,
            origin: message.msg.info.origin.position
          };
          setRobotData(prev => ({ ...prev, map: mapData }));
          updateMap(mapData);
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    ws.onclose = () => {
      setIsConnected(false);
    };

    ws.onerror = () => {
      setIsConnected(false);
    };

    return () => {
      ws.close();
    };
  }, [websocketUrl, topics, updateRobotPose, updateLaserScan, updateMap]);

  // Helper function to get topic type
  const getTopicType = (topic: string): string => {
    const typeMap: { [key: string]: string } = {
      '/odom': 'nav_msgs/Odometry',
      '/scan': 'sensor_msgs/LaserScan',
      '/map': 'nav_msgs/OccupancyGrid',
      '/tf': 'tf2_msgs/TFMessage'
    };
    return typeMap[topic] || '';
  };

  // Initialize Three.js on mount
  useEffect(() => {
    initThreeJS();
    
    return () => {
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
      }
      const currentMount = mountRef.current;
      const currentRenderer = rendererRef.current;
      if (currentRenderer && currentMount) {
        currentMount.removeChild(currentRenderer.domElement);
        currentRenderer.dispose();
      }
    };
  }, [initThreeJS]);

  // Camera controls
  const resetCamera = () => {
    if (cameraRef.current) {
      cameraRef.current.position.set(5, 5, 5);
      cameraRef.current.lookAt(0, 0, 0);
      setCameraPosition({ x: 5, y: 5, z: 5 });
    }
  };

  const followRobot = () => {
    if (cameraRef.current && robotData.position) {
      const { x, z } = robotData.position;
      cameraRef.current.position.set(x + 3, 3, z + 3);
      cameraRef.current.lookAt(x, 0, z);
      setCameraPosition({ x: x + 3, y: 3, z: z + 3 });
    }
  };

  return (
    <div className="three-visualization-container">
      <div className="three-visualization-header">
        <h3 className="three-visualization-title">
          <span className="three-visualization-icon">🎮</span>
          3D Robot Visualization
        </h3>
        <div className="three-controls">
          <button 
            className={`three-control-button ${showGrid ? 'active' : ''}`}
            onClick={() => setShowGrid(!showGrid)}
          >
            Grid
          </button>
          <button 
            className={`three-control-button ${showAxes ? 'active' : ''}`}
            onClick={() => setShowAxes(!showAxes)}
          >
            Axes
          </button>
          <button className="three-control-button" onClick={resetCamera}>
            Reset
          </button>
          <button className="three-control-button" onClick={followRobot}>
            Follow
          </button>
        </div>
      </div>
      
      <div className="three-canvas-container" style={{ width, height }}>
        <div ref={mountRef} />
        
        <div className="three-performance-stats">
          <div className="three-fps">FPS: {renderStats.fps}</div>
          <div className="three-triangles">Triangles: {renderStats.triangles}</div>
        </div>
        
        <div className="three-camera-info">
          <div className="three-camera-position">
            Pos: ({cameraPosition.x.toFixed(1)}, {cameraPosition.y.toFixed(1)}, {cameraPosition.z.toFixed(1)})
          </div>
          <div className="three-camera-target">Target: Robot</div>
        </div>
        
        <div className="three-scene-objects">
          <div className="three-object-count">Objects: {renderStats.objects}</div>
          <div className="three-object-list">
            Robot, Grid, Lights
            {robotData.laserScan && ', Laser Scan'}
            {robotData.map && ', Map'}
          </div>
        </div>
      </div>
      
      <div className="three-status-bar">
        <div className="three-render-info">
          <div className="three-render-stat">
            <div className="three-render-value">{robotData.position.x.toFixed(2)}</div>
            <div className="three-render-label">X</div>
          </div>
          <div className="three-render-stat">
            <div className="three-render-value">{robotData.position.y.toFixed(2)}</div>
            <div className="three-render-label">Y</div>
          </div>
          <div className="three-render-stat">
            <div className="three-render-value">{robotData.position.z.toFixed(2)}</div>
            <div className="three-render-label">Z</div>
          </div>
        </div>
        
        <div className={`three-connection-status ${isConnected ? 'three-connected' : 'three-disconnected'}`}>
          {isConnected ? 'Connected' : 'Disconnected'}
        </div>
      </div>
    </div>
  );
};

export default ThreeVisualization;
