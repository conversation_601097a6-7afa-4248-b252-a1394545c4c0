/* Map Viewer Component Styles */
.map-viewer-container {
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 20px;
}

.map-viewer-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.map-viewer-title {
  color: #9C27B0;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.map-viewer-icon {
  margin-right: 8px;
  font-size: 20px;
}

.map-canvas-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  border-radius: 4px;
  border: 1px solid #444;
  min-height: 300px;
  position: relative;
  overflow: hidden;
}

.map-canvas {
  max-width: 100%;
  max-height: 400px;
  border-radius: 4px;
  image-rendering: pixelated; /* For crisp occupancy grid display */
}

.map-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  padding: 40px;
}

.map-loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #333;
  border-top: 4px solid #9C27B0;
  border-radius: 50%;
  animation: mapSpin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes mapSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.map-status {
  font-size: 12px;
  color: #aaa;
  margin-top: 10px;
  text-align: center;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.map-info {
  display: flex;
  gap: 20px;
}

.map-robot-position {
  color: #E91E63;
  font-weight: 500;
}

.map-topic-info {
  color: #9C27B0;
}

.map-connection-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.map-connected {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid #4CAF50;
}

.map-disconnected {
  background-color: rgba(244, 67, 54, 0.2);
  color: #F44336;
  border: 1px solid #F44336;
}

/* Robot Position Indicator */
.robot-position-overlay {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #E91E63;
  border: 2px solid #fff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 10px rgba(233, 30, 99, 0.6);
  animation: robotPulse 2s infinite;
}

@keyframes robotPulse {
  0%, 100% { 
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  50% { 
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.8;
  }
}

/* Map Controls */
.map-controls {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 10px;
}

.map-control-button {
  background-color: #333;
  border: 1px solid #555;
  color: #fff;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s ease;
}

.map-control-button:hover {
  background-color: #444;
}

.map-control-button:active {
  background-color: #555;
}

.map-control-button.active {
  background-color: #9C27B0;
  border-color: #9C27B0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .map-viewer-container {
    padding: 15px;
  }
  
  .map-canvas-container {
    min-height: 250px;
  }
  
  .map-status {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
  
  .map-info {
    justify-content: center;
  }
}
