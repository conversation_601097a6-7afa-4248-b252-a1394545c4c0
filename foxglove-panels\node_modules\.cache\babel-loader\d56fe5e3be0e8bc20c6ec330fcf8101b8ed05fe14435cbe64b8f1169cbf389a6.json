{"ast": null, "code": "var _jsxFileName = \"F:\\\\foxglove\\\\foxglove-panels\\\\src\\\\components\\\\RobotDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\n\n// Configuration - Change these URLs to match your setup\n// Option 1: ROSBridge WebSocket (default)\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ROS_WEBSOCKET_URL = 'ws://192.168.1.192:8765';\n// Option 2: Foxglove Bridge WebSocket (uncomment line below and comment line above)\n// const ROS_WEBSOCKET_URL = 'ws://192.168.1.192:8765';\n\nconst RobotDashboard = () => {\n  _s();\n  const [robotData, setRobotData] = useState({\n    position: {\n      x: 0,\n      y: 0,\n      z: 0\n    },\n    orientation: {\n      x: 0,\n      y: 0,\n      z: 0,\n      w: 1\n    },\n    velocity: {\n      linear: 0,\n      angular: 0\n    },\n    battery: 85,\n    connected: false\n  });\n  const [cameraImage, setCameraImage] = useState(null);\n  const mapCanvasRef = useRef(null);\n\n  // Connect to ROS2 via ROSBridge WebSocket\n  useEffect(() => {\n    const wsUrl = ROS_WEBSOCKET_URL;\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('Connected to ROS2 WebSocket');\n      setRobotData(prev => ({\n        ...prev,\n        connected: true\n      }));\n\n      // Subscribe to odometry topic\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/odom',\n        type: 'nav_msgs/Odometry'\n      }));\n\n      // Subscribe to battery state (if available)\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/battery_state',\n        type: 'sensor_msgs/BatteryState'\n      }));\n\n      // Subscribe to cmd_vel to monitor velocity commands\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/cmd_vel',\n        type: 'geometry_msgs/Twist'\n      }));\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n        if (message.topic === '/odom' && message.msg) {\n          const pose = message.msg.pose.pose;\n          const twist = message.msg.twist.twist;\n          setRobotData(prev => ({\n            ...prev,\n            position: {\n              x: pose.position.x,\n              y: pose.position.y,\n              z: pose.position.z\n            },\n            orientation: {\n              x: pose.orientation.x,\n              y: pose.orientation.y,\n              z: pose.orientation.z,\n              w: pose.orientation.w\n            },\n            velocity: {\n              linear: Math.sqrt(twist.linear.x ** 2 + twist.linear.y ** 2),\n              angular: twist.angular.z\n            }\n          }));\n        }\n        if (message.topic === '/battery_state' && message.msg) {\n          setRobotData(prev => ({\n            ...prev,\n            battery: message.msg.percentage * 100\n          }));\n        }\n      } catch (error) {\n        console.error('Error parsing WebSocket message:', error);\n      }\n    };\n    ws.onclose = () => {\n      console.log('Disconnected from ROS2 WebSocket');\n      setRobotData(prev => ({\n        ...prev,\n        connected: false\n      }));\n    };\n    ws.onerror = error => {\n      console.error('WebSocket error:', error);\n      setRobotData(prev => ({\n        ...prev,\n        connected: false\n      }));\n    };\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n  // Connect to camera feed\n  useEffect(() => {\n    // Connect to camera topic via WebSocket\n    const wsUrl = ROS_WEBSOCKET_URL;\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      // Subscribe to camera topic\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/camera/image_raw',\n        type: 'sensor_msgs/Image'\n      }));\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n        if (message.topic === '/camera/image_raw' && message.msg) {\n          // Convert ROS image message to displayable format\n          const imageMsg = message.msg;\n          if (imageMsg.encoding === 'rgb8' || imageMsg.encoding === 'bgr8') {\n            // Create canvas to display image\n            const canvas = document.createElement('canvas');\n            canvas.width = imageMsg.width;\n            canvas.height = imageMsg.height;\n            const ctx = canvas.getContext('2d');\n            if (ctx) {\n              // Convert base64 data to image\n              const imageData = ctx.createImageData(imageMsg.width, imageMsg.height);\n              const data = atob(imageMsg.data);\n              for (let i = 0; i < data.length; i += 3) {\n                const pixelIndex = i / 3 * 4;\n                if (imageMsg.encoding === 'rgb8') {\n                  imageData.data[pixelIndex] = data.charCodeAt(i); // R\n                  imageData.data[pixelIndex + 1] = data.charCodeAt(i + 1); // G\n                  imageData.data[pixelIndex + 2] = data.charCodeAt(i + 2); // B\n                } else {\n                  // bgr8\n                  imageData.data[pixelIndex] = data.charCodeAt(i + 2); // R\n                  imageData.data[pixelIndex + 1] = data.charCodeAt(i + 1); // G\n                  imageData.data[pixelIndex + 2] = data.charCodeAt(i); // B\n                }\n                imageData.data[pixelIndex + 3] = 255; // A\n              }\n              ctx.putImageData(imageData, 0, 0);\n              setCameraImage(canvas.toDataURL());\n            }\n          }\n        }\n      } catch (error) {\n        console.error('Error processing camera message:', error);\n      }\n    };\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n  // Connect to map data and draw map with robot position\n  useEffect(() => {\n    const wsUrl = ROS_WEBSOCKET_URL;\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      // Subscribe to map topic\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/map',\n        type: 'nav_msgs/OccupancyGrid'\n      }));\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n        if (message.topic === '/map' && message.msg && mapCanvasRef.current) {\n          const mapMsg = message.msg;\n          const canvas = mapCanvasRef.current;\n          const ctx = canvas.getContext('2d');\n          if (ctx) {\n            canvas.width = mapMsg.info.width;\n            canvas.height = mapMsg.info.height;\n            const imageData = ctx.createImageData(mapMsg.info.width, mapMsg.info.height);\n\n            // Convert occupancy grid to image\n            for (let i = 0; i < mapMsg.data.length; i++) {\n              const value = mapMsg.data[i];\n              const pixelIndex = i * 4;\n              if (value === -1) {\n                // Unknown space - gray\n                imageData.data[pixelIndex] = 128; // R\n                imageData.data[pixelIndex + 1] = 128; // G\n                imageData.data[pixelIndex + 2] = 128; // B\n              } else if (value === 0) {\n                // Free space - white\n                imageData.data[pixelIndex] = 255; // R\n                imageData.data[pixelIndex + 1] = 255; // G\n                imageData.data[pixelIndex + 2] = 255; // B\n              } else {\n                // Occupied space - black\n                imageData.data[pixelIndex] = 0; // R\n                imageData.data[pixelIndex + 1] = 0; // G\n                imageData.data[pixelIndex + 2] = 0; // B\n              }\n              imageData.data[pixelIndex + 3] = 255; // A\n            }\n            ctx.putImageData(imageData, 0, 0);\n          }\n        }\n      } catch (error) {\n        console.error('Error processing map message:', error);\n      }\n    };\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n  // Draw robot position on map\n  useEffect(() => {\n    if (mapCanvasRef.current && robotData.connected) {\n      const canvas = mapCanvasRef.current;\n      const ctx = canvas.getContext('2d');\n      if (ctx) {\n        // Convert robot position to map coordinates\n        // This assumes map origin is at (0,0) - adjust based on your map setup\n        const robotX = robotData.position.x * 20 + canvas.width / 2; // Scale and center\n        const robotY = canvas.height - (robotData.position.y * 20 + canvas.height / 2); // Flip Y and center\n\n        // Draw robot as red circle\n        ctx.fillStyle = '#ff4444';\n        ctx.beginPath();\n        ctx.arc(robotX, robotY, 5, 0, 2 * Math.PI);\n        ctx.fill();\n\n        // Draw robot orientation arrow\n        const angle = Math.atan2(2 * (robotData.orientation.w * robotData.orientation.z + robotData.orientation.x * robotData.orientation.y), 1 - 2 * (robotData.orientation.y * robotData.orientation.y + robotData.orientation.z * robotData.orientation.z));\n        ctx.strokeStyle = '#fff';\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.moveTo(robotX, robotY);\n        ctx.lineTo(robotX + Math.cos(angle) * 15, robotY - Math.sin(angle) * 15 // Negative because canvas Y is flipped\n        );\n        ctx.stroke();\n      }\n    }\n  }, [robotData.position, robotData.orientation, robotData.connected]);\n  const getBatteryColor = level => {\n    if (level > 50) return '#4CAF50';\n    if (level > 20) return '#FF9800';\n    return '#F44336';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: '#0d1117',\n      minHeight: '100vh',\n      color: 'white',\n      fontFamily: 'monospace',\n      padding: '20px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        marginBottom: '30px',\n        borderBottom: '2px solid #333',\n        paddingBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          color: '#00bcd4',\n          margin: '0 0 10px 0'\n        },\n        children: \"\\uD83E\\uDD16 TurtleBot3 Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: '10px',\n          fontSize: '14px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: robotData.connected ? '#4CAF50' : '#F44336'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: robotData.connected ? 'CONNECTED TO ROS2' : 'DISCONNECTED'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            marginLeft: '20px',\n            fontSize: '12px',\n            color: '#666'\n          },\n          children: \"WebSocket: ws://localhost:9090 (ROSBridge)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n        gap: '20px',\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#4CAF50',\n            marginTop: 0\n          },\n          children: \"\\uD83D\\uDCCA Robot Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '5px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Position:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              color: '#aaa'\n            },\n            children: [\"X: \", robotData.position.x.toFixed(2), \"m | Y: \", robotData.position.y.toFixed(2), \"m | Z: \", robotData.position.z.toFixed(2), \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '5px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Velocity:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              color: '#aaa'\n            },\n            children: [\"Linear: \", robotData.velocity.linear.toFixed(2), \"m/s | Angular: \", robotData.velocity.angular.toFixed(2), \"rad/s\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '5px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Battery:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: getBatteryColor(robotData.battery)\n              },\n              children: [robotData.battery.toFixed(1), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100%',\n              height: '8px',\n              backgroundColor: '#333',\n              borderRadius: '4px',\n              overflow: 'hidden'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: `${robotData.battery}%`,\n                height: '100%',\n                backgroundColor: getBatteryColor(robotData.battery),\n                transition: 'width 0.3s ease'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#FF9800',\n            marginTop: 0\n          },\n          children: \"\\uD83D\\uDCF7 Camera Feed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '200px',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444'\n          },\n          children: cameraImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: cameraImage,\n            alt: \"Robot Camera\",\n            style: {\n              maxWidth: '100%',\n              maxHeight: '200px',\n              borderRadius: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#666',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '2rem',\n                marginBottom: '10px'\n              },\n              children: \"\\uD83D\\uDCF7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"No camera feed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#aaa',\n            marginTop: '10px',\n            textAlign: 'center'\n          },\n          children: \"Topic: /camera/image_raw | 10 FPS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#9C27B0',\n            marginTop: 0\n          },\n          children: \"\\uD83D\\uDDFA\\uFE0F Map & Navigation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n            ref: mapCanvasRef,\n            style: {\n              maxWidth: '100%',\n              borderRadius: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#aaa',\n            marginTop: '10px',\n            textAlign: 'center'\n          },\n          children: \"Topic: /map | Robot position tracked\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '20px',\n        borderTop: '1px solid #333',\n        color: '#666',\n        fontSize: '12px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\uD83C\\uDF10 TurtleBot3 Real-time Visualization via ROSBridge\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '5px'\n        },\n        children: \"Topics: /odom, /camera/image_raw, /map, /battery_state | WebSocket: ws://localhost:9090\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 479,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 471,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 292,\n    columnNumber: 5\n  }, this);\n};\n_s(RobotDashboard, \"ZdOdk/foE6unLdg9mRDHoMASPoA=\");\n_c = RobotDashboard;\nexport default RobotDashboard;\nvar _c;\n$RefreshReg$(_c, \"RobotDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "ROS_WEBSOCKET_URL", "RobotDashboard", "_s", "robotData", "setRobotData", "position", "x", "y", "z", "orientation", "w", "velocity", "linear", "angular", "battery", "connected", "cameraImage", "setCameraImage", "mapCanvasRef", "wsUrl", "ws", "WebSocket", "onopen", "console", "log", "prev", "send", "JSON", "stringify", "op", "topic", "type", "onmessage", "event", "message", "parse", "data", "msg", "pose", "twist", "Math", "sqrt", "percentage", "error", "onclose", "onerror", "close", "imageMsg", "encoding", "canvas", "document", "createElement", "width", "height", "ctx", "getContext", "imageData", "createImageData", "atob", "i", "length", "pixelIndex", "charCodeAt", "putImageData", "toDataURL", "current", "mapMsg", "info", "value", "robotX", "robotY", "fillStyle", "beginPath", "arc", "PI", "fill", "angle", "atan2", "strokeStyle", "lineWidth", "moveTo", "lineTo", "cos", "sin", "stroke", "getBatteryColor", "level", "style", "background", "minHeight", "color", "fontFamily", "padding", "children", "textAlign", "marginBottom", "borderBottom", "paddingBottom", "margin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "alignItems", "justifyContent", "gap", "fontSize", "borderRadius", "backgroundColor", "marginLeft", "gridTemplateColumns", "border", "marginTop", "toFixed", "overflow", "transition", "src", "alt", "max<PERSON><PERSON><PERSON>", "maxHeight", "ref", "borderTop", "_c", "$RefreshReg$"], "sources": ["F:/foxglove/foxglove-panels/src/components/RobotDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\n\n// Configuration - Change these URLs to match your setup\n// Option 1: ROSBridge WebSocket (default)\nconst ROS_WEBSOCKET_URL = 'ws://192.168.1.192:8765';\n// Option 2: Foxglove Bridge WebSocket (uncomment line below and comment line above)\n// const ROS_WEBSOCKET_URL = 'ws://192.168.1.192:8765';\n\ninterface RobotData {\n  position: { x: number; y: number; z: number };\n  orientation: { x: number; y: number; z: number; w: number };\n  velocity: { linear: number; angular: number };\n  battery: number;\n  connected: boolean;\n}\n\n\n\nconst RobotDashboard: React.FC = () => {\n  const [robotData, setRobotData] = useState<RobotData>({\n    position: { x: 0, y: 0, z: 0 },\n    orientation: { x: 0, y: 0, z: 0, w: 1 },\n    velocity: { linear: 0, angular: 0 },\n    battery: 85,\n    connected: false\n  });\n\n  const [cameraImage, setCameraImage] = useState<string | null>(null);\n  const mapCanvasRef = useRef<HTMLCanvasElement>(null);\n\n  // Connect to ROS2 via ROSBridge WebSocket\n  useEffect(() => {\n    const wsUrl = ROS_WEBSOCKET_URL;\n\n    const ws = new WebSocket(wsUrl);\n\n    ws.onopen = () => {\n      console.log('Connected to ROS2 WebSocket');\n      setRobotData(prev => ({ ...prev, connected: true }));\n\n      // Subscribe to odometry topic\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/odom',\n        type: 'nav_msgs/Odometry'\n      }));\n\n      // Subscribe to battery state (if available)\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/battery_state',\n        type: 'sensor_msgs/BatteryState'\n      }));\n\n      // Subscribe to cmd_vel to monitor velocity commands\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/cmd_vel',\n        type: 'geometry_msgs/Twist'\n      }));\n    };\n\n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n\n        if (message.topic === '/odom' && message.msg) {\n          const pose = message.msg.pose.pose;\n          const twist = message.msg.twist.twist;\n\n          setRobotData(prev => ({\n            ...prev,\n            position: {\n              x: pose.position.x,\n              y: pose.position.y,\n              z: pose.position.z\n            },\n            orientation: {\n              x: pose.orientation.x,\n              y: pose.orientation.y,\n              z: pose.orientation.z,\n              w: pose.orientation.w\n            },\n            velocity: {\n              linear: Math.sqrt(twist.linear.x ** 2 + twist.linear.y ** 2),\n              angular: twist.angular.z\n            }\n          }));\n        }\n\n        if (message.topic === '/battery_state' && message.msg) {\n          setRobotData(prev => ({\n            ...prev,\n            battery: message.msg.percentage * 100\n          }));\n        }\n\n      } catch (error) {\n        console.error('Error parsing WebSocket message:', error);\n      }\n    };\n\n    ws.onclose = () => {\n      console.log('Disconnected from ROS2 WebSocket');\n      setRobotData(prev => ({ ...prev, connected: false }));\n    };\n\n    ws.onerror = (error) => {\n      console.error('WebSocket error:', error);\n      setRobotData(prev => ({ ...prev, connected: false }));\n    };\n\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n  // Connect to camera feed\n  useEffect(() => {\n    // Connect to camera topic via WebSocket\n    const wsUrl = ROS_WEBSOCKET_URL;\n    const ws = new WebSocket(wsUrl);\n\n    ws.onopen = () => {\n      // Subscribe to camera topic\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/camera/image_raw',\n        type: 'sensor_msgs/Image'\n      }));\n    };\n\n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n\n        if (message.topic === '/camera/image_raw' && message.msg) {\n          // Convert ROS image message to displayable format\n          const imageMsg = message.msg;\n\n          if (imageMsg.encoding === 'rgb8' || imageMsg.encoding === 'bgr8') {\n            // Create canvas to display image\n            const canvas = document.createElement('canvas');\n            canvas.width = imageMsg.width;\n            canvas.height = imageMsg.height;\n            const ctx = canvas.getContext('2d');\n\n            if (ctx) {\n              // Convert base64 data to image\n              const imageData = ctx.createImageData(imageMsg.width, imageMsg.height);\n              const data = atob(imageMsg.data);\n\n              for (let i = 0; i < data.length; i += 3) {\n                const pixelIndex = (i / 3) * 4;\n                if (imageMsg.encoding === 'rgb8') {\n                  imageData.data[pixelIndex] = data.charCodeAt(i);     // R\n                  imageData.data[pixelIndex + 1] = data.charCodeAt(i + 1); // G\n                  imageData.data[pixelIndex + 2] = data.charCodeAt(i + 2); // B\n                } else { // bgr8\n                  imageData.data[pixelIndex] = data.charCodeAt(i + 2);     // R\n                  imageData.data[pixelIndex + 1] = data.charCodeAt(i + 1); // G\n                  imageData.data[pixelIndex + 2] = data.charCodeAt(i);     // B\n                }\n                imageData.data[pixelIndex + 3] = 255; // A\n              }\n\n              ctx.putImageData(imageData, 0, 0);\n              setCameraImage(canvas.toDataURL());\n            }\n          }\n        }\n      } catch (error) {\n        console.error('Error processing camera message:', error);\n      }\n    };\n\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n  // Connect to map data and draw map with robot position\n  useEffect(() => {\n    const wsUrl = ROS_WEBSOCKET_URL;\n    const ws = new WebSocket(wsUrl);\n\n    ws.onopen = () => {\n      // Subscribe to map topic\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/map',\n        type: 'nav_msgs/OccupancyGrid'\n      }));\n    };\n\n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n\n        if (message.topic === '/map' && message.msg && mapCanvasRef.current) {\n          const mapMsg = message.msg;\n          const canvas = mapCanvasRef.current;\n          const ctx = canvas.getContext('2d');\n\n          if (ctx) {\n            canvas.width = mapMsg.info.width;\n            canvas.height = mapMsg.info.height;\n\n            const imageData = ctx.createImageData(mapMsg.info.width, mapMsg.info.height);\n\n            // Convert occupancy grid to image\n            for (let i = 0; i < mapMsg.data.length; i++) {\n              const value = mapMsg.data[i];\n              const pixelIndex = i * 4;\n\n              if (value === -1) {\n                // Unknown space - gray\n                imageData.data[pixelIndex] = 128;     // R\n                imageData.data[pixelIndex + 1] = 128; // G\n                imageData.data[pixelIndex + 2] = 128; // B\n              } else if (value === 0) {\n                // Free space - white\n                imageData.data[pixelIndex] = 255;     // R\n                imageData.data[pixelIndex + 1] = 255; // G\n                imageData.data[pixelIndex + 2] = 255; // B\n              } else {\n                // Occupied space - black\n                imageData.data[pixelIndex] = 0;       // R\n                imageData.data[pixelIndex + 1] = 0;   // G\n                imageData.data[pixelIndex + 2] = 0;   // B\n              }\n              imageData.data[pixelIndex + 3] = 255; // A\n            }\n\n            ctx.putImageData(imageData, 0, 0);\n          }\n        }\n      } catch (error) {\n        console.error('Error processing map message:', error);\n      }\n    };\n\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n  // Draw robot position on map\n  useEffect(() => {\n    if (mapCanvasRef.current && robotData.connected) {\n      const canvas = mapCanvasRef.current;\n      const ctx = canvas.getContext('2d');\n\n      if (ctx) {\n        // Convert robot position to map coordinates\n        // This assumes map origin is at (0,0) - adjust based on your map setup\n        const robotX = (robotData.position.x * 20) + canvas.width / 2;  // Scale and center\n        const robotY = canvas.height - ((robotData.position.y * 20) + canvas.height / 2); // Flip Y and center\n\n        // Draw robot as red circle\n        ctx.fillStyle = '#ff4444';\n        ctx.beginPath();\n        ctx.arc(robotX, robotY, 5, 0, 2 * Math.PI);\n        ctx.fill();\n\n        // Draw robot orientation arrow\n        const angle = Math.atan2(\n          2 * (robotData.orientation.w * robotData.orientation.z + robotData.orientation.x * robotData.orientation.y),\n          1 - 2 * (robotData.orientation.y * robotData.orientation.y + robotData.orientation.z * robotData.orientation.z)\n        );\n\n        ctx.strokeStyle = '#fff';\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.moveTo(robotX, robotY);\n        ctx.lineTo(\n          robotX + Math.cos(angle) * 15,\n          robotY - Math.sin(angle) * 15  // Negative because canvas Y is flipped\n        );\n        ctx.stroke();\n      }\n    }\n  }, [robotData.position, robotData.orientation, robotData.connected]);\n\n  const getBatteryColor = (level: number): string => {\n    if (level > 50) return '#4CAF50';\n    if (level > 20) return '#FF9800';\n    return '#F44336';\n  };\n\n  return (\n    <div style={{\n      background: '#0d1117',\n      minHeight: '100vh',\n      color: 'white',\n      fontFamily: 'monospace',\n      padding: '20px'\n    }}>\n      {/* Header */}\n      <div style={{\n        textAlign: 'center',\n        marginBottom: '30px',\n        borderBottom: '2px solid #333',\n        paddingBottom: '20px'\n      }}>\n        <h1 style={{ color: '#00bcd4', margin: '0 0 10px 0' }}>\n          🤖 TurtleBot3 Dashboard\n        </h1>\n        <div style={{ \n          display: 'flex', \n          alignItems: 'center', \n          justifyContent: 'center', \n          gap: '10px',\n          fontSize: '14px'\n        }}>\n          <div style={{\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: robotData.connected ? '#4CAF50' : '#F44336'\n          }}></div>\n          <span>{robotData.connected ? 'CONNECTED TO ROS2' : 'DISCONNECTED'}</span>\n          <span style={{ marginLeft: '20px', fontSize: '12px', color: '#666' }}>\n            WebSocket: ws://localhost:9090 (ROSBridge)\n          </span>\n        </div>\n      </div>\n\n      {/* Main Dashboard Grid */}\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n        gap: '20px',\n        marginBottom: '20px'\n      }}>\n        \n        {/* Robot Status */}\n        <div style={{\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        }}>\n          <h3 style={{ color: '#4CAF50', marginTop: 0 }}>📊 Robot Status</h3>\n          \n          <div style={{ marginBottom: '15px' }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\n              <span>Position:</span>\n            </div>\n            <div style={{ fontSize: '12px', color: '#aaa' }}>\n              X: {robotData.position.x.toFixed(2)}m | \n              Y: {robotData.position.y.toFixed(2)}m | \n              Z: {robotData.position.z.toFixed(2)}m\n            </div>\n          </div>\n\n          <div style={{ marginBottom: '15px' }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\n              <span>Velocity:</span>\n            </div>\n            <div style={{ fontSize: '12px', color: '#aaa' }}>\n              Linear: {robotData.velocity.linear.toFixed(2)}m/s | \n              Angular: {robotData.velocity.angular.toFixed(2)}rad/s\n            </div>\n          </div>\n\n          <div style={{ marginBottom: '15px' }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\n              <span>Battery:</span>\n              <span style={{ color: getBatteryColor(robotData.battery) }}>\n                {robotData.battery.toFixed(1)}%\n              </span>\n            </div>\n            <div style={{\n              width: '100%',\n              height: '8px',\n              backgroundColor: '#333',\n              borderRadius: '4px',\n              overflow: 'hidden'\n            }}>\n              <div style={{\n                width: `${robotData.battery}%`,\n                height: '100%',\n                backgroundColor: getBatteryColor(robotData.battery),\n                transition: 'width 0.3s ease'\n              }}></div>\n            </div>\n          </div>\n        </div>\n\n        {/* Camera Feed */}\n        <div style={{\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        }}>\n          <h3 style={{ color: '#FF9800', marginTop: 0 }}>📷 Camera Feed</h3>\n          <div style={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '200px',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444'\n          }}>\n            {cameraImage ? (\n              <img \n                src={cameraImage} \n                alt=\"Robot Camera\" \n                style={{ \n                  maxWidth: '100%', \n                  maxHeight: '200px',\n                  borderRadius: '4px'\n                }} \n              />\n            ) : (\n              <div style={{ color: '#666', textAlign: 'center' }}>\n                <div style={{ fontSize: '2rem', marginBottom: '10px' }}>📷</div>\n                <div>No camera feed</div>\n              </div>\n            )}\n          </div>\n          <div style={{ \n            fontSize: '12px', \n            color: '#aaa', \n            marginTop: '10px',\n            textAlign: 'center'\n          }}>\n            Topic: /camera/image_raw | 10 FPS\n          </div>\n        </div>\n\n        {/* Map Viewer */}\n        <div style={{\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        }}>\n          <h3 style={{ color: '#9C27B0', marginTop: 0 }}>🗺️ Map & Navigation</h3>\n          <div style={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444'\n          }}>\n            <canvas \n              ref={mapCanvasRef}\n              style={{ \n                maxWidth: '100%',\n                borderRadius: '4px'\n              }}\n            />\n          </div>\n          <div style={{ \n            fontSize: '12px', \n            color: '#aaa', \n            marginTop: '10px',\n            textAlign: 'center'\n          }}>\n            Topic: /map | Robot position tracked\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div style={{\n        textAlign: 'center',\n        padding: '20px',\n        borderTop: '1px solid #333',\n        color: '#666',\n        fontSize: '12px'\n      }}>\n        <div>🌐 TurtleBot3 Real-time Visualization via ROSBridge</div>\n        <div style={{ marginTop: '5px' }}>\n          Topics: /odom, /camera/image_raw, /map, /battery_state | WebSocket: ws://localhost:9090\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RobotDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;;AAE1D;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,iBAAiB,GAAG,yBAAyB;AACnD;AACA;;AAYA,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAY;IACpDU,QAAQ,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAC9BC,WAAW,EAAE;MAAEH,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC;IACvCC,QAAQ,EAAE;MAAEC,MAAM,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAE,CAAC;IACnCC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAgB,IAAI,CAAC;EACnE,MAAMuB,YAAY,GAAGrB,MAAM,CAAoB,IAAI,CAAC;;EAEpD;EACAD,SAAS,CAAC,MAAM;IACd,MAAMuB,KAAK,GAAGnB,iBAAiB;IAE/B,MAAMoB,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChBC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1CpB,YAAY,CAACqB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEV,SAAS,EAAE;MAAK,CAAC,CAAC,CAAC;;MAEpD;MACAK,EAAE,CAACM,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QACrBC,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;;MAEH;MACAX,EAAE,CAACM,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QACrBC,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;;MAEH;MACAX,EAAE,CAACM,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QACrBC,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,UAAU;QACjBC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;IACL,CAAC;IAEDX,EAAE,CAACY,SAAS,GAAIC,KAAK,IAAK;MACxB,IAAI;QACF,MAAMC,OAAO,GAAGP,IAAI,CAACQ,KAAK,CAACF,KAAK,CAACG,IAAI,CAAC;QAEtC,IAAIF,OAAO,CAACJ,KAAK,KAAK,OAAO,IAAII,OAAO,CAACG,GAAG,EAAE;UAC5C,MAAMC,IAAI,GAAGJ,OAAO,CAACG,GAAG,CAACC,IAAI,CAACA,IAAI;UAClC,MAAMC,KAAK,GAAGL,OAAO,CAACG,GAAG,CAACE,KAAK,CAACA,KAAK;UAErCnC,YAAY,CAACqB,IAAI,KAAK;YACpB,GAAGA,IAAI;YACPpB,QAAQ,EAAE;cACRC,CAAC,EAAEgC,IAAI,CAACjC,QAAQ,CAACC,CAAC;cAClBC,CAAC,EAAE+B,IAAI,CAACjC,QAAQ,CAACE,CAAC;cAClBC,CAAC,EAAE8B,IAAI,CAACjC,QAAQ,CAACG;YACnB,CAAC;YACDC,WAAW,EAAE;cACXH,CAAC,EAAEgC,IAAI,CAAC7B,WAAW,CAACH,CAAC;cACrBC,CAAC,EAAE+B,IAAI,CAAC7B,WAAW,CAACF,CAAC;cACrBC,CAAC,EAAE8B,IAAI,CAAC7B,WAAW,CAACD,CAAC;cACrBE,CAAC,EAAE4B,IAAI,CAAC7B,WAAW,CAACC;YACtB,CAAC;YACDC,QAAQ,EAAE;cACRC,MAAM,EAAE4B,IAAI,CAACC,IAAI,CAACF,KAAK,CAAC3B,MAAM,CAACN,CAAC,IAAI,CAAC,GAAGiC,KAAK,CAAC3B,MAAM,CAACL,CAAC,IAAI,CAAC,CAAC;cAC5DM,OAAO,EAAE0B,KAAK,CAAC1B,OAAO,CAACL;YACzB;UACF,CAAC,CAAC,CAAC;QACL;QAEA,IAAI0B,OAAO,CAACJ,KAAK,KAAK,gBAAgB,IAAII,OAAO,CAACG,GAAG,EAAE;UACrDjC,YAAY,CAACqB,IAAI,KAAK;YACpB,GAAGA,IAAI;YACPX,OAAO,EAAEoB,OAAO,CAACG,GAAG,CAACK,UAAU,GAAG;UACpC,CAAC,CAAC,CAAC;QACL;MAEF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdpB,OAAO,CAACoB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;IACF,CAAC;IAEDvB,EAAE,CAACwB,OAAO,GAAG,MAAM;MACjBrB,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/CpB,YAAY,CAACqB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEV,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACvD,CAAC;IAEDK,EAAE,CAACyB,OAAO,GAAIF,KAAK,IAAK;MACtBpB,OAAO,CAACoB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxCvC,YAAY,CAACqB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEV,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACvD,CAAC;IAED,OAAO,MAAM;MACXK,EAAE,CAAC0B,KAAK,CAAC,CAAC;IACZ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlD,SAAS,CAAC,MAAM;IACd;IACA,MAAMuB,KAAK,GAAGnB,iBAAiB;IAC/B,MAAMoB,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChB;MACAF,EAAE,CAACM,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QACrBC,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;IACL,CAAC;IAEDX,EAAE,CAACY,SAAS,GAAIC,KAAK,IAAK;MACxB,IAAI;QACF,MAAMC,OAAO,GAAGP,IAAI,CAACQ,KAAK,CAACF,KAAK,CAACG,IAAI,CAAC;QAEtC,IAAIF,OAAO,CAACJ,KAAK,KAAK,mBAAmB,IAAII,OAAO,CAACG,GAAG,EAAE;UACxD;UACA,MAAMU,QAAQ,GAAGb,OAAO,CAACG,GAAG;UAE5B,IAAIU,QAAQ,CAACC,QAAQ,KAAK,MAAM,IAAID,QAAQ,CAACC,QAAQ,KAAK,MAAM,EAAE;YAChE;YACA,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;YAC/CF,MAAM,CAACG,KAAK,GAAGL,QAAQ,CAACK,KAAK;YAC7BH,MAAM,CAACI,MAAM,GAAGN,QAAQ,CAACM,MAAM;YAC/B,MAAMC,GAAG,GAAGL,MAAM,CAACM,UAAU,CAAC,IAAI,CAAC;YAEnC,IAAID,GAAG,EAAE;cACP;cACA,MAAME,SAAS,GAAGF,GAAG,CAACG,eAAe,CAACV,QAAQ,CAACK,KAAK,EAAEL,QAAQ,CAACM,MAAM,CAAC;cACtE,MAAMjB,IAAI,GAAGsB,IAAI,CAACX,QAAQ,CAACX,IAAI,CAAC;cAEhC,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,IAAI,CAACwB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;gBACvC,MAAME,UAAU,GAAIF,CAAC,GAAG,CAAC,GAAI,CAAC;gBAC9B,IAAIZ,QAAQ,CAACC,QAAQ,KAAK,MAAM,EAAE;kBAChCQ,SAAS,CAACpB,IAAI,CAACyB,UAAU,CAAC,GAAGzB,IAAI,CAAC0B,UAAU,CAACH,CAAC,CAAC,CAAC,CAAK;kBACrDH,SAAS,CAACpB,IAAI,CAACyB,UAAU,GAAG,CAAC,CAAC,GAAGzB,IAAI,CAAC0B,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;kBACzDH,SAAS,CAACpB,IAAI,CAACyB,UAAU,GAAG,CAAC,CAAC,GAAGzB,IAAI,CAAC0B,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC3D,CAAC,MAAM;kBAAE;kBACPH,SAAS,CAACpB,IAAI,CAACyB,UAAU,CAAC,GAAGzB,IAAI,CAAC0B,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,CAAK;kBACzDH,SAAS,CAACpB,IAAI,CAACyB,UAAU,GAAG,CAAC,CAAC,GAAGzB,IAAI,CAAC0B,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;kBACzDH,SAAS,CAACpB,IAAI,CAACyB,UAAU,GAAG,CAAC,CAAC,GAAGzB,IAAI,CAAC0B,UAAU,CAACH,CAAC,CAAC,CAAC,CAAK;gBAC3D;gBACAH,SAAS,CAACpB,IAAI,CAACyB,UAAU,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;cACxC;cAEAP,GAAG,CAACS,YAAY,CAACP,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;cACjCvC,cAAc,CAACgC,MAAM,CAACe,SAAS,CAAC,CAAC,CAAC;YACpC;UACF;QACF;MACF,CAAC,CAAC,OAAOrB,KAAK,EAAE;QACdpB,OAAO,CAACoB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;IACF,CAAC;IAED,OAAO,MAAM;MACXvB,EAAE,CAAC0B,KAAK,CAAC,CAAC;IACZ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlD,SAAS,CAAC,MAAM;IACd,MAAMuB,KAAK,GAAGnB,iBAAiB;IAC/B,MAAMoB,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChB;MACAF,EAAE,CAACM,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QACrBC,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;IACL,CAAC;IAEDX,EAAE,CAACY,SAAS,GAAIC,KAAK,IAAK;MACxB,IAAI;QACF,MAAMC,OAAO,GAAGP,IAAI,CAACQ,KAAK,CAACF,KAAK,CAACG,IAAI,CAAC;QAEtC,IAAIF,OAAO,CAACJ,KAAK,KAAK,MAAM,IAAII,OAAO,CAACG,GAAG,IAAInB,YAAY,CAAC+C,OAAO,EAAE;UACnE,MAAMC,MAAM,GAAGhC,OAAO,CAACG,GAAG;UAC1B,MAAMY,MAAM,GAAG/B,YAAY,CAAC+C,OAAO;UACnC,MAAMX,GAAG,GAAGL,MAAM,CAACM,UAAU,CAAC,IAAI,CAAC;UAEnC,IAAID,GAAG,EAAE;YACPL,MAAM,CAACG,KAAK,GAAGc,MAAM,CAACC,IAAI,CAACf,KAAK;YAChCH,MAAM,CAACI,MAAM,GAAGa,MAAM,CAACC,IAAI,CAACd,MAAM;YAElC,MAAMG,SAAS,GAAGF,GAAG,CAACG,eAAe,CAACS,MAAM,CAACC,IAAI,CAACf,KAAK,EAAEc,MAAM,CAACC,IAAI,CAACd,MAAM,CAAC;;YAE5E;YACA,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,MAAM,CAAC9B,IAAI,CAACwB,MAAM,EAAED,CAAC,EAAE,EAAE;cAC3C,MAAMS,KAAK,GAAGF,MAAM,CAAC9B,IAAI,CAACuB,CAAC,CAAC;cAC5B,MAAME,UAAU,GAAGF,CAAC,GAAG,CAAC;cAExB,IAAIS,KAAK,KAAK,CAAC,CAAC,EAAE;gBAChB;gBACAZ,SAAS,CAACpB,IAAI,CAACyB,UAAU,CAAC,GAAG,GAAG,CAAC,CAAK;gBACtCL,SAAS,CAACpB,IAAI,CAACyB,UAAU,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;gBACtCL,SAAS,CAACpB,IAAI,CAACyB,UAAU,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;cACxC,CAAC,MAAM,IAAIO,KAAK,KAAK,CAAC,EAAE;gBACtB;gBACAZ,SAAS,CAACpB,IAAI,CAACyB,UAAU,CAAC,GAAG,GAAG,CAAC,CAAK;gBACtCL,SAAS,CAACpB,IAAI,CAACyB,UAAU,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;gBACtCL,SAAS,CAACpB,IAAI,CAACyB,UAAU,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;cACxC,CAAC,MAAM;gBACL;gBACAL,SAAS,CAACpB,IAAI,CAACyB,UAAU,CAAC,GAAG,CAAC,CAAC,CAAO;gBACtCL,SAAS,CAACpB,IAAI,CAACyB,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAG;gBACtCL,SAAS,CAACpB,IAAI,CAACyB,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAG;cACxC;cACAL,SAAS,CAACpB,IAAI,CAACyB,UAAU,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;YACxC;YAEAP,GAAG,CAACS,YAAY,CAACP,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;UACnC;QACF;MACF,CAAC,CAAC,OAAOb,KAAK,EAAE;QACdpB,OAAO,CAACoB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;IACF,CAAC;IAED,OAAO,MAAM;MACXvB,EAAE,CAAC0B,KAAK,CAAC,CAAC;IACZ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlD,SAAS,CAAC,MAAM;IACd,IAAIsB,YAAY,CAAC+C,OAAO,IAAI9D,SAAS,CAACY,SAAS,EAAE;MAC/C,MAAMkC,MAAM,GAAG/B,YAAY,CAAC+C,OAAO;MACnC,MAAMX,GAAG,GAAGL,MAAM,CAACM,UAAU,CAAC,IAAI,CAAC;MAEnC,IAAID,GAAG,EAAE;QACP;QACA;QACA,MAAMe,MAAM,GAAIlE,SAAS,CAACE,QAAQ,CAACC,CAAC,GAAG,EAAE,GAAI2C,MAAM,CAACG,KAAK,GAAG,CAAC,CAAC,CAAE;QAChE,MAAMkB,MAAM,GAAGrB,MAAM,CAACI,MAAM,IAAKlD,SAAS,CAACE,QAAQ,CAACE,CAAC,GAAG,EAAE,GAAI0C,MAAM,CAACI,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;QAElF;QACAC,GAAG,CAACiB,SAAS,GAAG,SAAS;QACzBjB,GAAG,CAACkB,SAAS,CAAC,CAAC;QACflB,GAAG,CAACmB,GAAG,CAACJ,MAAM,EAAEC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG9B,IAAI,CAACkC,EAAE,CAAC;QAC1CpB,GAAG,CAACqB,IAAI,CAAC,CAAC;;QAEV;QACA,MAAMC,KAAK,GAAGpC,IAAI,CAACqC,KAAK,CACtB,CAAC,IAAI1E,SAAS,CAACM,WAAW,CAACC,CAAC,GAAGP,SAAS,CAACM,WAAW,CAACD,CAAC,GAAGL,SAAS,CAACM,WAAW,CAACH,CAAC,GAAGH,SAAS,CAACM,WAAW,CAACF,CAAC,CAAC,EAC3G,CAAC,GAAG,CAAC,IAAIJ,SAAS,CAACM,WAAW,CAACF,CAAC,GAAGJ,SAAS,CAACM,WAAW,CAACF,CAAC,GAAGJ,SAAS,CAACM,WAAW,CAACD,CAAC,GAAGL,SAAS,CAACM,WAAW,CAACD,CAAC,CAChH,CAAC;QAED8C,GAAG,CAACwB,WAAW,GAAG,MAAM;QACxBxB,GAAG,CAACyB,SAAS,GAAG,CAAC;QACjBzB,GAAG,CAACkB,SAAS,CAAC,CAAC;QACflB,GAAG,CAAC0B,MAAM,CAACX,MAAM,EAAEC,MAAM,CAAC;QAC1BhB,GAAG,CAAC2B,MAAM,CACRZ,MAAM,GAAG7B,IAAI,CAAC0C,GAAG,CAACN,KAAK,CAAC,GAAG,EAAE,EAC7BN,MAAM,GAAG9B,IAAI,CAAC2C,GAAG,CAACP,KAAK,CAAC,GAAG,EAAE,CAAE;QACjC,CAAC;QACDtB,GAAG,CAAC8B,MAAM,CAAC,CAAC;MACd;IACF;EACF,CAAC,EAAE,CAACjF,SAAS,CAACE,QAAQ,EAAEF,SAAS,CAACM,WAAW,EAAEN,SAAS,CAACY,SAAS,CAAC,CAAC;EAEpE,MAAMsE,eAAe,GAAIC,KAAa,IAAa;IACjD,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,SAAS;IAChC,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,SAAS;IAChC,OAAO,SAAS;EAClB,CAAC;EAED,oBACEvF,OAAA;IAAKwF,KAAK,EAAE;MACVC,UAAU,EAAE,SAAS;MACrBC,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,WAAW;MACvBC,OAAO,EAAE;IACX,CAAE;IAAAC,QAAA,gBAEA9F,OAAA;MAAKwF,KAAK,EAAE;QACVO,SAAS,EAAE,QAAQ;QACnBC,YAAY,EAAE,MAAM;QACpBC,YAAY,EAAE,gBAAgB;QAC9BC,aAAa,EAAE;MACjB,CAAE;MAAAJ,QAAA,gBACA9F,OAAA;QAAIwF,KAAK,EAAE;UAAEG,KAAK,EAAE,SAAS;UAAEQ,MAAM,EAAE;QAAa,CAAE;QAAAL,QAAA,EAAC;MAEvD;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLvG,OAAA;QAAKwF,KAAK,EAAE;UACVgB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,GAAG,EAAE,MAAM;UACXC,QAAQ,EAAE;QACZ,CAAE;QAAAd,QAAA,gBACA9F,OAAA;UAAKwF,KAAK,EAAE;YACVnC,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACduD,YAAY,EAAE,KAAK;YACnBC,eAAe,EAAE1G,SAAS,CAACY,SAAS,GAAG,SAAS,GAAG;UACrD;QAAE;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACTvG,OAAA;UAAA8F,QAAA,EAAO1F,SAAS,CAACY,SAAS,GAAG,mBAAmB,GAAG;QAAc;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzEvG,OAAA;UAAMwF,KAAK,EAAE;YAAEuB,UAAU,EAAE,MAAM;YAAEH,QAAQ,EAAE,MAAM;YAAEjB,KAAK,EAAE;UAAO,CAAE;UAAAG,QAAA,EAAC;QAEtE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvG,OAAA;MAAKwF,KAAK,EAAE;QACVgB,OAAO,EAAE,MAAM;QACfQ,mBAAmB,EAAE,sCAAsC;QAC3DL,GAAG,EAAE,MAAM;QACXX,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBAGA9F,OAAA;QAAKwF,KAAK,EAAE;UACVsB,eAAe,EAAE,SAAS;UAC1BG,MAAM,EAAE,gBAAgB;UACxBJ,YAAY,EAAE,KAAK;UACnBhB,OAAO,EAAE;QACX,CAAE;QAAAC,QAAA,gBACA9F,OAAA;UAAIwF,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEuB,SAAS,EAAE;UAAE,CAAE;UAAApB,QAAA,EAAC;QAAe;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEnEvG,OAAA;UAAKwF,KAAK,EAAE;YAAEQ,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBACnC9F,OAAA;YAAKwF,KAAK,EAAE;cAAEgB,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE,eAAe;cAAEV,YAAY,EAAE;YAAM,CAAE;YAAAF,QAAA,eACpF9F,OAAA;cAAA8F,QAAA,EAAM;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACNvG,OAAA;YAAKwF,KAAK,EAAE;cAAEoB,QAAQ,EAAE,MAAM;cAAEjB,KAAK,EAAE;YAAO,CAAE;YAAAG,QAAA,GAAC,KAC5C,EAAC1F,SAAS,CAACE,QAAQ,CAACC,CAAC,CAAC4G,OAAO,CAAC,CAAC,CAAC,EAAC,SACjC,EAAC/G,SAAS,CAACE,QAAQ,CAACE,CAAC,CAAC2G,OAAO,CAAC,CAAC,CAAC,EAAC,SACjC,EAAC/G,SAAS,CAACE,QAAQ,CAACG,CAAC,CAAC0G,OAAO,CAAC,CAAC,CAAC,EAAC,GACtC;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvG,OAAA;UAAKwF,KAAK,EAAE;YAAEQ,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBACnC9F,OAAA;YAAKwF,KAAK,EAAE;cAAEgB,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE,eAAe;cAAEV,YAAY,EAAE;YAAM,CAAE;YAAAF,QAAA,eACpF9F,OAAA;cAAA8F,QAAA,EAAM;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACNvG,OAAA;YAAKwF,KAAK,EAAE;cAAEoB,QAAQ,EAAE,MAAM;cAAEjB,KAAK,EAAE;YAAO,CAAE;YAAAG,QAAA,GAAC,UACvC,EAAC1F,SAAS,CAACQ,QAAQ,CAACC,MAAM,CAACsG,OAAO,CAAC,CAAC,CAAC,EAAC,iBACrC,EAAC/G,SAAS,CAACQ,QAAQ,CAACE,OAAO,CAACqG,OAAO,CAAC,CAAC,CAAC,EAAC,OAClD;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvG,OAAA;UAAKwF,KAAK,EAAE;YAAEQ,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBACnC9F,OAAA;YAAKwF,KAAK,EAAE;cAAEgB,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE,eAAe;cAAEV,YAAY,EAAE;YAAM,CAAE;YAAAF,QAAA,gBACpF9F,OAAA;cAAA8F,QAAA,EAAM;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrBvG,OAAA;cAAMwF,KAAK,EAAE;gBAAEG,KAAK,EAAEL,eAAe,CAAClF,SAAS,CAACW,OAAO;cAAE,CAAE;cAAA+E,QAAA,GACxD1F,SAAS,CAACW,OAAO,CAACoG,OAAO,CAAC,CAAC,CAAC,EAAC,GAChC;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNvG,OAAA;YAAKwF,KAAK,EAAE;cACVnC,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,KAAK;cACbwD,eAAe,EAAE,MAAM;cACvBD,YAAY,EAAE,KAAK;cACnBO,QAAQ,EAAE;YACZ,CAAE;YAAAtB,QAAA,eACA9F,OAAA;cAAKwF,KAAK,EAAE;gBACVnC,KAAK,EAAE,GAAGjD,SAAS,CAACW,OAAO,GAAG;gBAC9BuC,MAAM,EAAE,MAAM;gBACdwD,eAAe,EAAExB,eAAe,CAAClF,SAAS,CAACW,OAAO,CAAC;gBACnDsG,UAAU,EAAE;cACd;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvG,OAAA;QAAKwF,KAAK,EAAE;UACVsB,eAAe,EAAE,SAAS;UAC1BG,MAAM,EAAE,gBAAgB;UACxBJ,YAAY,EAAE,KAAK;UACnBhB,OAAO,EAAE;QACX,CAAE;QAAAC,QAAA,gBACA9F,OAAA;UAAIwF,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEuB,SAAS,EAAE;UAAE,CAAE;UAAApB,QAAA,EAAC;QAAc;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClEvG,OAAA;UAAKwF,KAAK,EAAE;YACVgB,OAAO,EAAE,MAAM;YACfE,cAAc,EAAE,QAAQ;YACxBD,UAAU,EAAE,QAAQ;YACpBf,SAAS,EAAE,OAAO;YAClBoB,eAAe,EAAE,MAAM;YACvBD,YAAY,EAAE,KAAK;YACnBI,MAAM,EAAE;UACV,CAAE;UAAAnB,QAAA,EACC7E,WAAW,gBACVjB,OAAA;YACEsH,GAAG,EAAErG,WAAY;YACjBsG,GAAG,EAAC,cAAc;YAClB/B,KAAK,EAAE;cACLgC,QAAQ,EAAE,MAAM;cAChBC,SAAS,EAAE,OAAO;cAClBZ,YAAY,EAAE;YAChB;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEFvG,OAAA;YAAKwF,KAAK,EAAE;cAAEG,KAAK,EAAE,MAAM;cAAEI,SAAS,EAAE;YAAS,CAAE;YAAAD,QAAA,gBACjD9F,OAAA;cAAKwF,KAAK,EAAE;gBAAEoB,QAAQ,EAAE,MAAM;gBAAEZ,YAAY,EAAE;cAAO,CAAE;cAAAF,QAAA,EAAC;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChEvG,OAAA;cAAA8F,QAAA,EAAK;YAAc;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNvG,OAAA;UAAKwF,KAAK,EAAE;YACVoB,QAAQ,EAAE,MAAM;YAChBjB,KAAK,EAAE,MAAM;YACbuB,SAAS,EAAE,MAAM;YACjBnB,SAAS,EAAE;UACb,CAAE;UAAAD,QAAA,EAAC;QAEH;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvG,OAAA;QAAKwF,KAAK,EAAE;UACVsB,eAAe,EAAE,SAAS;UAC1BG,MAAM,EAAE,gBAAgB;UACxBJ,YAAY,EAAE,KAAK;UACnBhB,OAAO,EAAE;QACX,CAAE;QAAAC,QAAA,gBACA9F,OAAA;UAAIwF,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEuB,SAAS,EAAE;UAAE,CAAE;UAAApB,QAAA,EAAC;QAAoB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEvG,OAAA;UAAKwF,KAAK,EAAE;YACVgB,OAAO,EAAE,MAAM;YACfE,cAAc,EAAE,QAAQ;YACxBD,UAAU,EAAE,QAAQ;YACpBK,eAAe,EAAE,MAAM;YACvBD,YAAY,EAAE,KAAK;YACnBI,MAAM,EAAE;UACV,CAAE;UAAAnB,QAAA,eACA9F,OAAA;YACE0H,GAAG,EAAEvG,YAAa;YAClBqE,KAAK,EAAE;cACLgC,QAAQ,EAAE,MAAM;cAChBX,YAAY,EAAE;YAChB;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNvG,OAAA;UAAKwF,KAAK,EAAE;YACVoB,QAAQ,EAAE,MAAM;YAChBjB,KAAK,EAAE,MAAM;YACbuB,SAAS,EAAE,MAAM;YACjBnB,SAAS,EAAE;UACb,CAAE;UAAAD,QAAA,EAAC;QAEH;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvG,OAAA;MAAKwF,KAAK,EAAE;QACVO,SAAS,EAAE,QAAQ;QACnBF,OAAO,EAAE,MAAM;QACf8B,SAAS,EAAE,gBAAgB;QAC3BhC,KAAK,EAAE,MAAM;QACbiB,QAAQ,EAAE;MACZ,CAAE;MAAAd,QAAA,gBACA9F,OAAA;QAAA8F,QAAA,EAAK;MAAmD;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC9DvG,OAAA;QAAKwF,KAAK,EAAE;UAAE0B,SAAS,EAAE;QAAM,CAAE;QAAApB,QAAA,EAAC;MAElC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpG,EAAA,CAldID,cAAwB;AAAA0H,EAAA,GAAxB1H,cAAwB;AAod9B,eAAeA,cAAc;AAAC,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}