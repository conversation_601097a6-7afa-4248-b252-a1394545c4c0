import React from 'react';

interface ConnectionStatusProps {
  isConnected: boolean;
  websocketUrl: string;
  batteryLevel: number;
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ isConnected, websocketUrl, batteryLevel }) => {
  const getBatteryColor = (level: number): string => {
    if (level > 50) return '#4CAF50';
    if (level > 20) return '#FF9800';
    return '#F44336';
  };

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      backgroundColor: '#1a1a1a',
      padding: '15px 20px',
      borderRadius: '8px',
      border: '1px solid #333',
      marginBottom: '20px'
    }}>
      <div>
        <h1 style={{ margin: 0, color: '#00ff88', fontSize: '24px' }}>
          🤖 ROS2 Robot Dashboard
        </h1>
        <span style={{ marginLeft: '20px', fontSize: '12px', color: '#666' }}>
          WebSocket: {websocketUrl} (ROSBridge)
        </span>
      </div>
      <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
        <div style={{
          padding: '8px 16px',
          borderRadius: '20px',
          backgroundColor: isConnected ? '#1a4a1a' : '#4a1a1a',
          border: `1px solid ${isConnected ? '#00ff88' : '#ff4444'}`,
          fontSize: '12px',
          fontWeight: 'bold',
          color: isConnected ? '#00ff88' : '#ff4444'
        }}>
          {isConnected ? '🟢 CONNECTED' : '🔴 DISCONNECTED'}
        </div>
        <div style={{
          padding: '8px 16px',
          borderRadius: '20px',
          backgroundColor: '#1a1a1a',
          border: `1px solid ${getBatteryColor(batteryLevel)}`,
          fontSize: '12px',
          fontWeight: 'bold',
          color: getBatteryColor(batteryLevel)
        }}>
          🔋 {batteryLevel}%
        </div>
      </div>
    </div>
  );
};

export default ConnectionStatus;
