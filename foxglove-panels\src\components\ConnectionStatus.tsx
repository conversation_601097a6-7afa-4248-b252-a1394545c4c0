import React, { useState, useEffect } from 'react';
import '../styles/ConnectionStatus.css';

interface ConnectionStatusProps {
  isConnected: boolean;
  websocketUrl: string;
  batteryLevel: number;
  messageCount?: number;
  errorCount?: number;
  uptime?: number;
  onToggleConnection?: () => void;
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  isConnected,
  websocketUrl,
  batteryLevel,
  messageCount = 0,
  errorCount = 0,
  uptime = 0,
  onToggleConnection
}) => {
  const [connectionHealth, setConnectionHealth] = useState(100);
  const [startTime] = useState(new Date());

  const getBatteryColor = (level: number): string => {
    if (level > 50) return '#4CAF50';
    if (level > 20) return '#FF9800';
    return '#F44336';
  };

  const getHealthColor = (health: number): string => {
    if (health > 80) return 'health-excellent';
    if (health > 60) return 'health-good';
    if (health > 40) return 'health-fair';
    return 'health-poor';
  };

  const formatUptime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Calculate connection health based on error rate
  useEffect(() => {
    if (messageCount > 0) {
      const errorRate = errorCount / messageCount;
      const health = Math.max(0, 100 - (errorRate * 100));
      setConnectionHealth(health);
    }
  }, [messageCount, errorCount]);

  const currentUptime = Math.floor((new Date().getTime() - startTime.getTime()) / 1000);

  return (
    <div className="connection-status-container">
      <div className="connection-status-header">
        <h1 className="connection-status-title">
          <span className="connection-status-icon">🤖</span>
          ROS2 Robot Dashboard
        </h1>
        {onToggleConnection && (
          <button
            className={`connection-toggle-button ${isConnected ? 'connected' : 'disconnected'}`}
            onClick={onToggleConnection}
          >
            {isConnected ? 'Disconnect' : 'Connect'}
          </button>
        )}
      </div>

      <div className="connection-details">
        <div className="connection-detail-item">
          <div className="connection-detail-label">WebSocket URL</div>
          <div className="connection-detail-value connection-url">{websocketUrl}</div>
        </div>

        <div className="connection-detail-item">
          <div className="connection-detail-label">Connection Status</div>
          <div className={`connection-status-indicator ${
            isConnected ? 'status-connected' : 'status-disconnected'
          }`}>
            {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
          </div>
        </div>

        <div className="connection-detail-item">
          <div className="connection-detail-label">Battery Level</div>
          <div
            className="connection-detail-value"
            style={{ color: getBatteryColor(batteryLevel) }}
          >
            🔋 {batteryLevel}%
          </div>
        </div>

        <div className="connection-detail-item">
          <div className="connection-detail-label">Protocol</div>
          <div className="connection-detail-value">ROSBridge WebSocket</div>
        </div>
      </div>

      <div className="connection-stats">
        <div className="connection-stat">
          <div className="connection-stat-value stat-uptime">
            {formatUptime(uptime || currentUptime)}
          </div>
          <div className="connection-stat-label">Uptime</div>
        </div>

        <div className="connection-stat">
          <div className="connection-stat-value stat-messages">
            {messageCount.toLocaleString()}
          </div>
          <div className="connection-stat-label">Messages</div>
        </div>

        <div className="connection-stat">
          <div className="connection-stat-value stat-errors">
            {errorCount}
          </div>
          <div className="connection-stat-label">Errors</div>
        </div>
      </div>

      <div className="connection-health">
        <div className="connection-health-label">Connection Health:</div>
        <div className="connection-health-bar">
          <div
            className={`connection-health-fill ${getHealthColor(connectionHealth)}`}
            style={{ width: `${connectionHealth}%` }}
          />
        </div>
      </div>
    </div>
  );
};

export default ConnectionStatus;
