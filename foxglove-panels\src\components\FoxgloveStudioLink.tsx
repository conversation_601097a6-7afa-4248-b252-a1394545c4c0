import React from 'react';

const FoxgloveStudioLink: React.FC = () => {
  // Generate Foxglove Studio URL with your ROS2 connection
  const generateFoxgloveUrl = () => {
    const baseUrl = 'https://studio.foxglove.dev/';
    
    // Create a layout configuration for your specific needs
    const layoutConfig = {
      "configById": {
        "3D!4co6n9d": {
          "layers": {
            "grid": { "layerId": "grid" },
            "tf": { "layerId": "tf" }
          },
          "cameraState": {
            "perspective": true,
            "distance": 20,
            "phi": 60,
            "thetaOffset": 45,
            "targetOffset": [0, 0, 0],
            "target": [0, 0, 0],
            "targetOrientation": [0, 0, 0, 1]
          },
          "followMode": "follow-pose",
          "scene": {
            "transforms": {
              "showLabel": false,
              "editable": true
            }
          }
        },
        "Plot!3xbqnp": {
          "paths": [
            {
              "timestampMethod": "receiveTime",
              "value": "/odom.pose.pose.position.x",
              "enabled": true,
              "label": "Position X"
            },
            {
              "timestampMethod": "receiveTime", 
              "value": "/odom.pose.pose.position.y",
              "enabled": true,
              "label": "Position Y"
            }
          ],
          "showXAxisLabels": true,
          "showYAxisLabels": true,
          "showLegend": true,
          "legendDisplay": "floating",
          "showPlotValuesInLegend": false,
          "isSynced": true,
          "xAxisVal": "timestamp",
          "sidebarDimension": 240
        },
        "RawMessages!2w7s9m": {
          "diffEnabled": false,
          "diffMethod": "custom",
          "diffTopicPath": "",
          "showFullMessageForDiff": false,
          "topicPath": "/tf"
        },
        "Image!1a2b3c": {
          "cameraTopic": "/camera/image_raw",
          "enabledMarkerTopics": [],
          "scale": 0.2,
          "pan": { "x": 0, "y": 0 },
          "rotation": 0,
          "flipHorizontal": false,
          "flipVertical": false,
          "minValue": 0,
          "maxValue": 10000
        }
      },
      "globalVariables": {},
      "userNodes": {},
      "linkedGlobalVariables": [],
      "playbackConfig": {
        "speed": 1,
        "messageOrder": "receiveTime"
      },
      "layout": {
        "direction": "row",
        "first": {
          "direction": "column",
          "first": "3D!4co6n9d",
          "second": {
            "direction": "row",
            "first": "Plot!3xbqnp",
            "second": "RawMessages!2w7s9m",
            "splitPercentage": 50
          },
          "splitPercentage": 60
        },
        "second": "Image!1a2b3c",
        "splitPercentage": 70
      }
    };

    // Encode the layout as URL parameter
    const layoutParam = encodeURIComponent(JSON.stringify(layoutConfig));
    
    // Your ROS2 connection parameters
    const connectionParams = new URLSearchParams({
      'ds': 'rosbridge-websocket',
      'ds.url': 'ws://*************:9090',
      'layoutId': 'custom',
      'layout': layoutParam
    });

    return `${baseUrl}?${connectionParams.toString()}`;
  };

  const openFoxgloveStudio = () => {
    const url = generateFoxgloveUrl();
    window.open(url, '_blank');
  };

  return (
    <div style={{
      backgroundColor: '#1a1a1a',
      border: '2px solid #4CAF50',
      borderRadius: '12px',
      padding: '24px',
      margin: '20px 0',
      textAlign: 'center'
    }}>
      <h2 style={{ color: '#4CAF50', marginTop: 0 }}>
        🦊 Open in Foxglove Studio
      </h2>
      
      <p style={{ color: '#aaa', marginBottom: '20px' }}>
        Launch Foxglove Studio with pre-configured layout for your TurtleBot3:
      </p>
      
      <div style={{ 
        backgroundColor: '#000', 
        padding: '15px', 
        borderRadius: '8px',
        marginBottom: '20px',
        fontFamily: 'monospace',
        fontSize: '12px',
        color: '#00ff00'
      }}>
        <div>✅ 3D Visualization Panel</div>
        <div>✅ Position Plot (/odom → position.x, position.y)</div>
        <div>✅ Raw Messages Panel (/tf)</div>
        <div>✅ Camera Feed (/camera/image_raw)</div>
        <div>✅ Pre-connected to: ws://*************:9090</div>
      </div>

      <button
        onClick={openFoxgloveStudio}
        style={{
          backgroundColor: '#4CAF50',
          color: 'white',
          border: 'none',
          padding: '12px 24px',
          borderRadius: '6px',
          fontSize: '16px',
          cursor: 'pointer',
          fontWeight: 'bold',
          transition: 'background-color 0.2s'
        }}
        onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#45a049'}
        onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#4CAF50'}
      >
        🚀 Launch Foxglove Studio
      </button>
      
      <div style={{ 
        fontSize: '12px', 
        color: '#666', 
        marginTop: '15px' 
      }}>
        Opens in new tab with your ROS2 data source pre-configured
      </div>
    </div>
  );
};

export default FoxgloveStudioLink;
