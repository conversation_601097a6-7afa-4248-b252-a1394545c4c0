import React, { useState, useEffect } from 'react';
import FoxgloveStudioLink from './FoxgloveStudioLink';
import MapViewer from './MapViewer';
import PositionDisplay from './PositionDisplay';
import SensorDataDisplay from './SensorDataDisplay';
import ConnectionStatus from './ConnectionStatus';
import ThreeVisualization from './ThreeVisualization';
import '../styles/Dashboard.css';

// Configuration - Change these URLs to match your setup
// Option 1: ROSBridge WebSocket (default)
const ROS_WEBSOCKET_URL = 'ws://192.168.1.192:9090';
// Option 2: Foxglove Bridge WebSocket (uncomment line below and comment line above)
// const ROS_WEBSOCKET_URL = 'ws://192.168.1.192:8765';

interface RobotData {
  position: { x: number; y: number; z: number };
  orientation: { x: number; y: number; z: number; w: number };
  velocity: { linear: number; angular: number };
  battery: number;
  connected: boolean;
}



const RobotDashboard: React.FC = () => {
  const [robotData, setRobotData] = useState<RobotData>({
    position: { x: 0, y: 0, z: 0 },
    orientation: { x: 0, y: 0, z: 0, w: 1 },
    velocity: { linear: 0, angular: 0 },
    battery: 0, // Will be updated from real battery data
    connected: false
  });

  const [plotDataCount, setPlotDataCount] = useState<number>(0);
  const [tfMessages, setTfMessages] = useState<any[]>([]);
  const [sensorData, setSensorData] = useState<any[]>([]);
  const [cameraImage, setCameraImage] = useState<string | null>(null);

  // Connect to ROS2 via ROSBridge WebSocket
  useEffect(() => {
    const wsUrl = ROS_WEBSOCKET_URL;

    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      console.log('Connected to ROS2 WebSocket');
      setRobotData(prev => ({ ...prev, connected: true }));

      // Subscribe to odometry topic
      ws.send(JSON.stringify({
        op: 'subscribe',
        topic: '/odom',
        type: 'nav_msgs/Odometry'
      }));

      // Subscribe to battery state (if available)
      ws.send(JSON.stringify({
        op: 'subscribe',
        topic: '/battery_state',
        type: 'sensor_msgs/BatteryState'
      }));

      // Subscribe to cmd_vel to monitor velocity commands
      ws.send(JSON.stringify({
        op: 'subscribe',
        topic: '/cmd_vel',
        type: 'geometry_msgs/Twist'
      }));

      // Subscribe to tf for transform data (like Webviz)
      ws.send(JSON.stringify({
        op: 'subscribe',
        topic: '/tf',
        type: 'tf2_msgs/TFMessage'
      }));

      // Subscribe to scan data for 3D visualization
      ws.send(JSON.stringify({
        op: 'subscribe',
        topic: '/scan',
        type: 'sensor_msgs/LaserScan'
      }));
    };

    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);

        if (message.topic === '/odom' && message.msg) {
          const pose = message.msg.pose.pose;
          const twist = message.msg.twist.twist;

          setRobotData(prev => ({
            ...prev,
            position: {
              x: pose.position.x,
              y: pose.position.y,
              z: pose.position.z
            },
            orientation: {
              x: pose.orientation.x,
              y: pose.orientation.y,
              z: pose.orientation.z,
              w: pose.orientation.w
            },
            velocity: {
              linear: Math.sqrt(twist.linear.x ** 2 + twist.linear.y ** 2),
              angular: twist.angular.z
            }
          }));

          // Count received data points
          setPlotDataCount(prev => prev + 1);
        }

        if (message.topic === '/tf' && message.msg) {
          // Store TF messages (like Webviz Raw Messages panel)
          setTfMessages(prev => [message.msg, ...prev.slice(0, 9)]); // Keep last 10 messages
        }

        if (message.topic === '/scan' && message.msg) {
          // Store sensor data for 3D visualization
          setSensorData(prev => [message.msg, ...prev.slice(0, 4)]); // Keep last 5 scans
        }

        if (message.topic === '/battery_state' && message.msg) {
          setRobotData(prev => ({
            ...prev,
            battery: message.msg.percentage * 100
          }));
        }

      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    ws.onclose = () => {
      console.log('Disconnected from ROS2 WebSocket');
      setRobotData(prev => ({ ...prev, connected: false }));
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      setRobotData(prev => ({ ...prev, connected: false }));
    };

    return () => {
      ws.close();
    };
  }, []);

  // Connect to camera feed
  useEffect(() => {
    // Connect to camera topic via WebSocket
    const wsUrl = ROS_WEBSOCKET_URL;
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      // Subscribe to camera topic
      ws.send(JSON.stringify({
        op: 'subscribe',
        topic: '/camera/image_raw',
        type: 'sensor_msgs/Image'
      }));
    };

    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);

        if (message.topic === '/camera/image_raw' && message.msg) {
          // Convert ROS image message to displayable format
          const imageMsg = message.msg;

          if (imageMsg.encoding === 'rgb8' || imageMsg.encoding === 'bgr8') {
            // Create canvas to display image
            const canvas = document.createElement('canvas');
            canvas.width = imageMsg.width;
            canvas.height = imageMsg.height;
            const ctx = canvas.getContext('2d');

            if (ctx) {
              // Convert base64 data to image
              const imageData = ctx.createImageData(imageMsg.width, imageMsg.height);
              const data = atob(imageMsg.data);

              for (let i = 0; i < data.length; i += 3) {
                const pixelIndex = (i / 3) * 4;
                if (imageMsg.encoding === 'rgb8') {
                  imageData.data[pixelIndex] = data.charCodeAt(i);     // R
                  imageData.data[pixelIndex + 1] = data.charCodeAt(i + 1); // G
                  imageData.data[pixelIndex + 2] = data.charCodeAt(i + 2); // B
                } else { // bgr8
                  imageData.data[pixelIndex] = data.charCodeAt(i + 2);     // R
                  imageData.data[pixelIndex + 1] = data.charCodeAt(i + 1); // G
                  imageData.data[pixelIndex + 2] = data.charCodeAt(i);     // B
                }
                imageData.data[pixelIndex + 3] = 255; // A
              }

              ctx.putImageData(imageData, 0, 0);
              setCameraImage(canvas.toDataURL());
            }
          }
        }
      } catch (error) {
        console.error('Error processing camera message:', error);
      }
    };

    return () => {
      ws.close();
    };
  }, []);







  return (
    <div className="dashboard-container">
      <ConnectionStatus
        isConnected={robotData.connected}
        websocketUrl={ROS_WEBSOCKET_URL}
        batteryLevel={robotData.battery}
        messageCount={plotDataCount + sensorData.length + tfMessages.length}
        errorCount={0}
      />

      {/* Three.js Visualization Panel */}
      <div className="dashboard-visualization-section">
        <ThreeVisualization
          websocketUrl={ROS_WEBSOCKET_URL}
          topics={['/odom', '/scan', '/map']}
          width={800}
          height={400}
        />
      </div>

      {/* Main Dashboard Grid - Webviz Style Layout */}
      <div className="dashboard-grid">

        <PositionDisplay
          position={robotData.position}
          orientation={robotData.orientation}
          dataPointsCount={plotDataCount}
          isConnected={robotData.connected}
        />
        
        {/* Raw Messages Panel - TF Messages (like Webviz) */}
        <div className="dashboard-panel">
          <h3 className="dashboard-panel-title">
            <span className="dashboard-panel-icon">📋</span>
            Raw Messages (/tf)
          </h3>
          <div className="dashboard-raw-messages">
            {tfMessages.length > 0 ? (
              tfMessages.map((msg, index) => (
                <div key={index} className={`dashboard-message-item ${index === 0 ? 'latest' : ''}`}>
                  <div className="dashboard-message-header">
                    Transform #{index + 1}:
                  </div>
                  <div className="dashboard-message-content">
                    Frames: {msg.transforms?.[0]?.header?.frame_id || 'N/A'} → {msg.transforms?.[0]?.child_frame_id || 'N/A'}
                  </div>
                  {msg.transforms?.[0]?.transform && (
                    <div className="dashboard-message-content">
                      Translation: [{msg.transforms[0].transform.translation.x?.toFixed(3)}, {msg.transforms[0].transform.translation.y?.toFixed(3)}, {msg.transforms[0].transform.translation.z?.toFixed(3)}]
                    </div>
                  )}
                </div>
              ))
            ) : (
              <div className="dashboard-loading-message">
                Waiting for /tf messages...
              </div>
            )}
          </div>
          <div className="dashboard-panel-status">
            Messages received: {tfMessages.length} | Topic: /tf
          </div>
        </div>

        {/* Robot Status */}
        <div className="dashboard-panel">
          <h3 className="dashboard-panel-title">
            <span className="dashboard-panel-icon">📊</span>
            Robot Status
          </h3>

          <div className="dashboard-status-item">
            <div className="dashboard-status-label">Position:</div>
            <div className="dashboard-status-value">
              X: {robotData.position.x.toFixed(2)}m |
              Y: {robotData.position.y.toFixed(2)}m |
              Z: {robotData.position.z.toFixed(2)}m
            </div>
          </div>

          <div className="dashboard-status-item">
            <div className="dashboard-status-label">Velocity:</div>
            <div className="dashboard-status-value">
              Linear: {robotData.velocity.linear.toFixed(2)}m/s |
              Angular: {robotData.velocity.angular.toFixed(2)}rad/s
            </div>
          </div>

          <div className="dashboard-status-item">
            <div className="dashboard-status-label">
              Battery:
              <span
                className="dashboard-battery-value"
                style={{ color: robotData.battery > 50 ? '#4CAF50' : robotData.battery > 20 ? '#FF9800' : '#F44336' }}
              >
                {robotData.battery.toFixed(1)}%
              </span>
            </div>
            <div className="dashboard-battery-bar">
              <div
                className="dashboard-battery-fill"
                style={{
                  width: `${robotData.battery}%`,
                  backgroundColor: robotData.battery > 50 ? '#4CAF50' : robotData.battery > 20 ? '#FF9800' : '#F44336'
                }}
              />
            </div>
          </div>
        </div>

        {/* Camera Feed */}
        <div className="dashboard-panel">
          <h3 className="dashboard-panel-title">
            <span className="dashboard-panel-icon">📷</span>
            Camera Feed
          </h3>
          <div className="dashboard-camera-container">
            {cameraImage ? (
              <img
                src={cameraImage}
                alt="Robot Camera"
                className="dashboard-camera-image"
              />
            ) : (
              <div className="dashboard-camera-placeholder">
                <div className="dashboard-camera-icon">📷</div>
                <div>No camera feed</div>
              </div>
            )}
          </div>
          <div className="dashboard-panel-status">
            Topic: /camera/image_raw | 10 FPS
          </div>
        </div>

        <SensorDataDisplay
          sensorData={sensorData}
          isConnected={robotData.connected}
        />

        <MapViewer
          websocketUrl={ROS_WEBSOCKET_URL}
          robotPosition={robotData.position}
          isConnected={robotData.connected}
        />
      </div>

      {/* Foxglove Studio Link */}
      <FoxgloveStudioLink />

      {/* Footer */}
      <div className="dashboard-footer">
        <div className="dashboard-footer-title">🌐 TurtleBot3 Three.js Dashboard via ROSBridge</div>
        <div className="dashboard-footer-details">
          Topics: /odom, /tf, /scan, /camera/image_raw, /map | WebSocket: {ROS_WEBSOCKET_URL}
        </div>
      </div>
    </div>
  );
};

export default RobotDashboard;
