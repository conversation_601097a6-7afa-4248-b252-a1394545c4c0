import React, { useState, useEffect, useRef } from 'react';
import FoxgloveStudioLink from './FoxgloveStudioLink';

// Configuration - Change these URLs to match your setup
// Option 1: ROSBridge WebSocket (default)
const ROS_WEBSOCKET_URL = 'ws://192.168.1.192:9090';
// Option 2: Foxglove Bridge WebSocket (uncomment line below and comment line above)
// const ROS_WEBSOCKET_URL = 'ws://192.168.1.192:8765';

interface RobotData {
  position: { x: number; y: number; z: number };
  orientation: { x: number; y: number; z: number; w: number };
  velocity: { linear: number; angular: number };
  battery: number;
  connected: boolean;
}



const RobotDashboard: React.FC = () => {
  const [robotData, setRobotData] = useState<RobotData>({
    position: { x: 0, y: 0, z: 0 },
    orientation: { x: 0, y: 0, z: 0, w: 1 },
    velocity: { linear: 0, angular: 0 },
    battery: 0, // Will be updated from real battery data
    connected: false
  });

  const [plotDataCount, setPlotDataCount] = useState<number>(0);
  const [tfMessages, setTfMessages] = useState<any[]>([]);
  const [sensorData, setSensorData] = useState<any[]>([]);
  const [cameraImage, setCameraImage] = useState<string | null>(null);

  // Connect to ROS2 via ROSBridge WebSocket
  useEffect(() => {
    const wsUrl = ROS_WEBSOCKET_URL;

    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      console.log('Connected to ROS2 WebSocket');
      setRobotData(prev => ({ ...prev, connected: true }));

      // Subscribe to odometry topic
      ws.send(JSON.stringify({
        op: 'subscribe',
        topic: '/odom',
        type: 'nav_msgs/Odometry'
      }));

      // Subscribe to battery state (if available)
      ws.send(JSON.stringify({
        op: 'subscribe',
        topic: '/battery_state',
        type: 'sensor_msgs/BatteryState'
      }));

      // Subscribe to cmd_vel to monitor velocity commands
      ws.send(JSON.stringify({
        op: 'subscribe',
        topic: '/cmd_vel',
        type: 'geometry_msgs/Twist'
      }));

      // Subscribe to tf for transform data (like Webviz)
      ws.send(JSON.stringify({
        op: 'subscribe',
        topic: '/tf',
        type: 'tf2_msgs/TFMessage'
      }));

      // Subscribe to scan data for 3D visualization
      ws.send(JSON.stringify({
        op: 'subscribe',
        topic: '/scan',
        type: 'sensor_msgs/LaserScan'
      }));
    };

    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);

        if (message.topic === '/odom' && message.msg) {
          const pose = message.msg.pose.pose;
          const twist = message.msg.twist.twist;

          setRobotData(prev => ({
            ...prev,
            position: {
              x: pose.position.x,
              y: pose.position.y,
              z: pose.position.z
            },
            orientation: {
              x: pose.orientation.x,
              y: pose.orientation.y,
              z: pose.orientation.z,
              w: pose.orientation.w
            },
            velocity: {
              linear: Math.sqrt(twist.linear.x ** 2 + twist.linear.y ** 2),
              angular: twist.angular.z
            }
          }));

          // Count received data points
          setPlotDataCount(prev => prev + 1);
        }

        if (message.topic === '/tf' && message.msg) {
          // Store TF messages (like Webviz Raw Messages panel)
          setTfMessages(prev => [message.msg, ...prev.slice(0, 9)]); // Keep last 10 messages
        }

        if (message.topic === '/scan' && message.msg) {
          // Store sensor data for 3D visualization
          setSensorData(prev => [message.msg, ...prev.slice(0, 4)]); // Keep last 5 scans
        }

        if (message.topic === '/battery_state' && message.msg) {
          setRobotData(prev => ({
            ...prev,
            battery: message.msg.percentage * 100
          }));
        }

      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    ws.onclose = () => {
      console.log('Disconnected from ROS2 WebSocket');
      setRobotData(prev => ({ ...prev, connected: false }));
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      setRobotData(prev => ({ ...prev, connected: false }));
    };

    return () => {
      ws.close();
    };
  }, []);

  // Connect to camera feed
  useEffect(() => {
    // Connect to camera topic via WebSocket
    const wsUrl = ROS_WEBSOCKET_URL;
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      // Subscribe to camera topic
      ws.send(JSON.stringify({
        op: 'subscribe',
        topic: '/camera/image_raw',
        type: 'sensor_msgs/Image'
      }));
    };

    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);

        if (message.topic === '/camera/image_raw' && message.msg) {
          // Convert ROS image message to displayable format
          const imageMsg = message.msg;

          if (imageMsg.encoding === 'rgb8' || imageMsg.encoding === 'bgr8') {
            // Create canvas to display image
            const canvas = document.createElement('canvas');
            canvas.width = imageMsg.width;
            canvas.height = imageMsg.height;
            const ctx = canvas.getContext('2d');

            if (ctx) {
              // Convert base64 data to image
              const imageData = ctx.createImageData(imageMsg.width, imageMsg.height);
              const data = atob(imageMsg.data);

              for (let i = 0; i < data.length; i += 3) {
                const pixelIndex = (i / 3) * 4;
                if (imageMsg.encoding === 'rgb8') {
                  imageData.data[pixelIndex] = data.charCodeAt(i);     // R
                  imageData.data[pixelIndex + 1] = data.charCodeAt(i + 1); // G
                  imageData.data[pixelIndex + 2] = data.charCodeAt(i + 2); // B
                } else { // bgr8
                  imageData.data[pixelIndex] = data.charCodeAt(i + 2);     // R
                  imageData.data[pixelIndex + 1] = data.charCodeAt(i + 1); // G
                  imageData.data[pixelIndex + 2] = data.charCodeAt(i);     // B
                }
                imageData.data[pixelIndex + 3] = 255; // A
              }

              ctx.putImageData(imageData, 0, 0);
              setCameraImage(canvas.toDataURL());
            }
          }
        }
      } catch (error) {
        console.error('Error processing camera message:', error);
      }
    };

    return () => {
      ws.close();
    };
  }, []);



  const getBatteryColor = (level: number): string => {
    if (level > 50) return '#4CAF50';
    if (level > 20) return '#FF9800';
    return '#F44336';
  };

  return (
    <div style={{
      background: '#0d1117',
      minHeight: '100vh',
      color: 'white',
      fontFamily: 'monospace',
      padding: '20px'
    }}>
      {/* Header */}
      <div style={{
        textAlign: 'center',
        marginBottom: '30px',
        borderBottom: '2px solid #333',
        paddingBottom: '20px'
      }}>
        <h1 style={{ color: '#00bcd4', margin: '0 0 10px 0' }}>
          🤖 TurtleBot3 Dashboard
        </h1>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center', 
          gap: '10px',
          fontSize: '14px'
        }}>
          <div style={{
            width: '12px',
            height: '12px',
            borderRadius: '50%',
            backgroundColor: robotData.connected ? '#4CAF50' : '#F44336'
          }}></div>
          <span>{robotData.connected ? 'CONNECTED TO ROS2' : 'DISCONNECTED'}</span>
          <span style={{ marginLeft: '20px', fontSize: '12px', color: '#666' }}>
            WebSocket: {ROS_WEBSOCKET_URL} (ROSBridge)
          </span>
        </div>
      </div>

      {/* Main Dashboard Grid - Webviz Style Layout */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '20px',
        marginBottom: '20px'
      }}>

        {/* Position Data Display */}
        <div style={{
          backgroundColor: '#1a1a1a',
          border: '1px solid #333',
          borderRadius: '8px',
          padding: '20px'
        }}>
          <h3 style={{ color: '#00bcd4', marginTop: 0 }}>📈 Position Data (/odom)</h3>
          <div style={{
            backgroundColor: '#000',
            borderRadius: '4px',
            border: '1px solid #444',
            padding: '15px'
          }}>
            <div style={{ marginBottom: '10px' }}>
              <strong style={{ color: '#00bcd4' }}>Current Position:</strong>
            </div>
            <div style={{ fontSize: '14px', color: '#aaa', marginBottom: '8px' }}>
              X: {robotData.position.x.toFixed(3)}m
            </div>
            <div style={{ fontSize: '14px', color: '#aaa', marginBottom: '8px' }}>
              Y: {robotData.position.y.toFixed(3)}m
            </div>
            <div style={{ fontSize: '14px', color: '#aaa' }}>
              Z: {robotData.position.z.toFixed(3)}m
            </div>
          </div>
          <div style={{ fontSize: '12px', color: '#aaa', marginTop: '10px' }}>
            Data points received: {plotDataCount}
          </div>
        </div>
        
        {/* Raw Messages Panel - TF Messages (like Webviz) */}
        <div style={{
          backgroundColor: '#1a1a1a',
          border: '1px solid #333',
          borderRadius: '8px',
          padding: '20px'
        }}>
          <h3 style={{ color: '#FF9800', marginTop: 0 }}>📋 Raw Messages (/tf)</h3>
          <div style={{
            height: '200px',
            backgroundColor: '#000',
            borderRadius: '4px',
            border: '1px solid #444',
            padding: '10px',
            overflow: 'auto',
            fontFamily: 'monospace',
            fontSize: '11px'
          }}>
            {tfMessages.length > 0 ? (
              tfMessages.map((msg, index) => (
                <div key={index} style={{
                  marginBottom: '8px',
                  color: index === 0 ? '#00ff00' : '#aaa',
                  borderBottom: '1px solid #333',
                  paddingBottom: '4px'
                }}>
                  <div style={{ color: '#00bcd4' }}>
                    Transform #{index + 1}:
                  </div>
                  <div>
                    Frames: {msg.transforms?.[0]?.header?.frame_id || 'N/A'} → {msg.transforms?.[0]?.child_frame_id || 'N/A'}
                  </div>
                  {msg.transforms?.[0]?.transform && (
                    <div>
                      Translation: [{msg.transforms[0].transform.translation.x?.toFixed(3)}, {msg.transforms[0].transform.translation.y?.toFixed(3)}, {msg.transforms[0].transform.translation.z?.toFixed(3)}]
                    </div>
                  )}
                </div>
              ))
            ) : (
              <div style={{ color: '#666', textAlign: 'center', marginTop: '80px' }}>
                Waiting for /tf messages...
              </div>
            )}
          </div>
          <div style={{ fontSize: '12px', color: '#aaa', marginTop: '10px' }}>
            Messages received: {tfMessages.length} | Topic: /tf
          </div>
        </div>

        {/* Robot Status */}
        <div style={{
          backgroundColor: '#1a1a1a',
          border: '1px solid #333',
          borderRadius: '8px',
          padding: '20px'
        }}>
          <h3 style={{ color: '#4CAF50', marginTop: 0 }}>📊 Robot Status</h3>
          
          <div style={{ marginBottom: '15px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
              <span>Position:</span>
            </div>
            <div style={{ fontSize: '12px', color: '#aaa' }}>
              X: {robotData.position.x.toFixed(2)}m | 
              Y: {robotData.position.y.toFixed(2)}m | 
              Z: {robotData.position.z.toFixed(2)}m
            </div>
          </div>

          <div style={{ marginBottom: '15px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
              <span>Velocity:</span>
            </div>
            <div style={{ fontSize: '12px', color: '#aaa' }}>
              Linear: {robotData.velocity.linear.toFixed(2)}m/s | 
              Angular: {robotData.velocity.angular.toFixed(2)}rad/s
            </div>
          </div>

          <div style={{ marginBottom: '15px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
              <span>Battery:</span>
              <span style={{ color: getBatteryColor(robotData.battery) }}>
                {robotData.battery.toFixed(1)}%
              </span>
            </div>
            <div style={{
              width: '100%',
              height: '8px',
              backgroundColor: '#333',
              borderRadius: '4px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${robotData.battery}%`,
                height: '100%',
                backgroundColor: getBatteryColor(robotData.battery),
                transition: 'width 0.3s ease'
              }}></div>
            </div>
          </div>
        </div>

        {/* Camera Feed */}
        <div style={{
          backgroundColor: '#1a1a1a',
          border: '1px solid #333',
          borderRadius: '8px',
          padding: '20px'
        }}>
          <h3 style={{ color: '#FF9800', marginTop: 0 }}>📷 Camera Feed</h3>
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '200px',
            backgroundColor: '#000',
            borderRadius: '4px',
            border: '1px solid #444'
          }}>
            {cameraImage ? (
              <img 
                src={cameraImage} 
                alt="Robot Camera" 
                style={{ 
                  maxWidth: '100%', 
                  maxHeight: '200px',
                  borderRadius: '4px'
                }} 
              />
            ) : (
              <div style={{ color: '#666', textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', marginBottom: '10px' }}>📷</div>
                <div>No camera feed</div>
              </div>
            )}
          </div>
          <div style={{ 
            fontSize: '12px', 
            color: '#aaa', 
            marginTop: '10px',
            textAlign: 'center'
          }}>
            Topic: /camera/image_raw | 10 FPS
          </div>
        </div>

        {/* Sensor Data Display */}
        <div style={{
          backgroundColor: '#1a1a1a',
          border: '1px solid #333',
          borderRadius: '8px',
          padding: '20px'
        }}>
          <h3 style={{ color: '#E91E63', marginTop: 0 }}>🌐 Sensor Data (/scan)</h3>
          <div style={{
            backgroundColor: '#000',
            borderRadius: '4px',
            border: '1px solid #444',
            padding: '15px'
          }}>
            {sensorData.length > 0 ? (
              <div>
                <div style={{ marginBottom: '10px' }}>
                  <strong style={{ color: '#E91E63' }}>Latest Scan:</strong>
                </div>
                <div style={{ fontSize: '14px', color: '#aaa', marginBottom: '8px' }}>
                  Points: {sensorData[0]?.ranges?.length || 0}
                </div>
                <div style={{ fontSize: '14px', color: '#aaa', marginBottom: '8px' }}>
                  Range: {sensorData[0]?.range_min?.toFixed(2) || 'N/A'}m - {sensorData[0]?.range_max?.toFixed(1) || 'N/A'}m
                </div>
                <div style={{ fontSize: '14px', color: '#aaa' }}>
                  Angle: {((sensorData[0]?.angle_min || 0) * 180 / Math.PI).toFixed(1)}° - {((sensorData[0]?.angle_max || 0) * 180 / Math.PI).toFixed(1)}°
                </div>
              </div>
            ) : (
              <div style={{ color: '#666', textAlign: 'center', padding: '20px' }}>
                Waiting for sensor data...
              </div>
            )}
          </div>
          <div style={{ fontSize: '12px', color: '#aaa', marginTop: '10px' }}>
            Scans received: {sensorData.length}
          </div>
        </div>

        {/* Map Status */}
        <div style={{
          backgroundColor: '#1a1a1a',
          border: '1px solid #333',
          borderRadius: '8px',
          padding: '20px'
        }}>
          <h3 style={{ color: '#9C27B0', marginTop: 0 }}>🗺️ Map Status</h3>
          <div style={{
            backgroundColor: '#000',
            borderRadius: '4px',
            border: '1px solid #444',
            padding: '15px'
          }}>
            <div style={{ marginBottom: '10px' }}>
              <strong style={{ color: '#9C27B0' }}>Map Topic:</strong>
            </div>
            <div style={{ fontSize: '14px', color: '#aaa', marginBottom: '8px' }}>
              Topic: /map
            </div>
            <div style={{ fontSize: '14px', color: '#aaa', marginBottom: '8px' }}>
              Status: {robotData.connected ? 'Connected' : 'Disconnected'}
            </div>
            <div style={{ fontSize: '14px', color: '#aaa' }}>
              Use Foxglove Studio below for map visualization
            </div>
          </div>
        </div>
      </div>

      {/* Foxglove Studio Link */}
      <FoxgloveStudioLink />

      {/* Footer */}
      <div style={{
        textAlign: 'center',
        padding: '20px',
        borderTop: '1px solid #333',
        color: '#666',
        fontSize: '12px'
      }}>
        <div>🌐 TurtleBot3 Webviz-Style Dashboard via ROSBridge</div>
        <div style={{ marginTop: '5px' }}>
          Topics: /odom (plot), /tf (raw messages), /scan (3D), /camera/image_raw, /map | WebSocket: {ROS_WEBSOCKET_URL}
        </div>
      </div>
    </div>
  );
};

export default RobotDashboard;
