/* Main Dashboard Styles */
.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  color: #ffffff;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  padding: 20px;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.dashboard-panel {
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 20px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dashboard-panel:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.panel-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.panel-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.panel-icon {
  margin-right: 8px;
  font-size: 20px;
}

.panel-content {
  background-color: #000;
  border-radius: 4px;
  border: 1px solid #444;
  padding: 15px;
  min-height: 120px;
}

.panel-footer {
  font-size: 12px;
  color: #aaa;
  margin-top: 10px;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .dashboard-panel {
    padding: 15px;
  }
}

/* Loading States */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #333;
  border-radius: 50%;
  border-top-color: #00bcd4;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Status Indicators */
.status-connected {
  color: #4CAF50;
}

.status-disconnected {
  color: #F44336;
}

.status-warning {
  color: #FF9800;
}

/* Data Display */
.data-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 4px 0;
}

.data-label {
  color: #aaa;
  font-weight: 500;
}

.data-value {
  color: #fff;
  font-family: 'Courier New', monospace;
}

/* Footer */
.dashboard-footer {
  text-align: center;
  padding: 20px;
  border-top: 1px solid #333;
  color: #666;
  font-size: 12px;
  margin-top: 40px;
}
