import React from 'react';

interface SensorData {
  ranges?: number[];
  range_min?: number;
  range_max?: number;
  angle_min?: number;
  angle_max?: number;
}

interface SensorDataDisplayProps {
  sensorData: SensorData[];
}

const SensorDataDisplay: React.FC<SensorDataDisplayProps> = ({ sensorData }) => {
  return (
    <div style={{
      backgroundColor: '#1a1a1a',
      border: '1px solid #333',
      borderRadius: '8px',
      padding: '20px'
    }}>
      <h3 style={{ color: '#E91E63', marginTop: 0 }}>🌐 Sensor Data (/scan)</h3>
      <div style={{
        backgroundColor: '#000',
        borderRadius: '4px',
        border: '1px solid #444',
        padding: '15px'
      }}>
        {sensorData.length > 0 ? (
          <div>
            <div style={{ marginBottom: '10px' }}>
              <strong style={{ color: '#E91E63' }}>Latest Scan:</strong>
            </div>
            <div style={{ fontSize: '14px', color: '#aaa', marginBottom: '8px' }}>
              Points: {sensorData[0]?.ranges?.length || 0}
            </div>
            <div style={{ fontSize: '14px', color: '#aaa', marginBottom: '8px' }}>
              Range: {sensorData[0]?.range_min?.toFixed(2) || 'N/A'}m - {sensorData[0]?.range_max?.toFixed(1) || 'N/A'}m
            </div>
            <div style={{ fontSize: '14px', color: '#aaa' }}>
              Angle: {((sensorData[0]?.angle_min || 0) * 180 / Math.PI).toFixed(1)}° - {((sensorData[0]?.angle_max || 0) * 180 / Math.PI).toFixed(1)}°
            </div>
          </div>
        ) : (
          <div style={{ color: '#666', textAlign: 'center', padding: '20px' }}>
            Waiting for sensor data...
          </div>
        )}
      </div>
      <div style={{ fontSize: '12px', color: '#aaa', marginTop: '10px' }}>
        Scans received: {sensorData.length}
      </div>
    </div>
  );
};

export default SensorDataDisplay;
