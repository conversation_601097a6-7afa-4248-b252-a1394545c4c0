import React, { useState, useEffect, useRef } from 'react';
import '../styles/SensorDataDisplay.css';

interface SensorData {
  ranges?: number[];
  range_min?: number;
  range_max?: number;
  angle_min?: number;
  angle_max?: number;
  angle_increment?: number;
  time_increment?: number;
  scan_time?: number;
}

interface SensorDataDisplayProps {
  sensorData: SensorData[];
  isConnected: boolean;
  websocketUrl?: string;
}

const SensorDataDisplay: React.FC<SensorDataDisplayProps> = ({ sensorData, isConnected }) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const latestScan = sensorData.length > 0 ? sensorData[sensorData.length - 1] : null;

  // Calculate statistics from latest scan
  const getStats = () => {
    if (!latestScan?.ranges) return null;

    const validRanges = latestScan.ranges.filter(r => r > 0 && r < 30);
    if (validRanges.length === 0) return null;

    return {
      min: Math.min(...validRanges),
      max: Math.max(...validRanges),
      avg: validRanges.reduce((a, b) => a + b, 0) / validRanges.length,
      count: validRanges.length
    };
  };

  const stats = getStats();

  // Update animation when new data arrives
  useEffect(() => {
    if (sensorData.length > 0) {
      setIsUpdating(true);
      const timer = setTimeout(() => setIsUpdating(false), 500);
      return () => clearTimeout(timer);
    }
  }, [sensorData]);

  // Draw laser scan visualization
  useEffect(() => {
    if (!canvasRef.current || !latestScan?.ranges) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const scale = 8; // pixels per meter

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw polar grid
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 1;

    // Concentric circles
    for (let r = 1; r <= 5; r++) {
      ctx.beginPath();
      ctx.arc(centerX, centerY, r * scale * 2, 0, 2 * Math.PI);
      ctx.stroke();
    }

    // Radial lines
    for (let angle = 0; angle < 2 * Math.PI; angle += Math.PI / 4) {
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.lineTo(
        centerX + Math.cos(angle) * 5 * scale * 2,
        centerY + Math.sin(angle) * 5 * scale * 2
      );
      ctx.stroke();
    }

    // Draw laser points
    const { ranges, angle_min = 0, angle_increment = 0 } = latestScan;

    ranges.forEach((range, index) => {
      if (range > 0 && range < 30) {
        const angle = angle_min + index * angle_increment;
        const x = centerX + range * Math.cos(angle) * scale;
        const y = centerY + range * Math.sin(angle) * scale;

        // Color based on distance
        if (range < 1) {
          ctx.fillStyle = '#F44336'; // Red for close
        } else if (range < 3) {
          ctx.fillStyle = '#FF9800'; // Orange for medium
        } else {
          ctx.fillStyle = '#4CAF50'; // Green for far
        }

        ctx.beginPath();
        ctx.arc(x, y, 2, 0, 2 * Math.PI);
        ctx.fill();
      }
    });

    // Draw robot position
    ctx.fillStyle = '#E91E63';
    ctx.beginPath();
    ctx.arc(centerX, centerY, 4, 0, 2 * Math.PI);
    ctx.fill();

  }, [latestScan]);

  return (
    <div className={`sensor-display-container ${isUpdating ? 'sensor-data-update' : ''}`}>
      <div className="sensor-display-header">
        <h3 className="sensor-display-title">
          <span className="sensor-display-icon">📡</span>
          Laser Scan Data
        </h3>
      </div>

      <div className="sensor-data-container">
        <div className="sensor-visualization">
          {latestScan ? (
            <canvas
              ref={canvasRef}
              className="sensor-canvas"
              width={300}
              height={200}
            />
          ) : (
            <div className="sensor-loading">
              <div className="sensor-loading-spinner"></div>
              <div>Waiting for laser scan data...</div>
            </div>
          )}
        </div>

        {stats && (
          <div className="sensor-data-stats">
            <div className="sensor-stat-item">
              <div className="sensor-stat-label">Min Range</div>
              <div className="sensor-stat-value sensor-range-min">{stats.min.toFixed(2)}m</div>
            </div>
            <div className="sensor-stat-item">
              <div className="sensor-stat-label">Max Range</div>
              <div className="sensor-stat-value sensor-range-max">{stats.max.toFixed(2)}m</div>
            </div>
            <div className="sensor-stat-item">
              <div className="sensor-stat-label">Avg Range</div>
              <div className="sensor-stat-value sensor-range-avg">{stats.avg.toFixed(2)}m</div>
            </div>
            <div className="sensor-stat-item">
              <div className="sensor-stat-label">Points</div>
              <div className="sensor-stat-value sensor-point-count">{stats.count}</div>
            </div>
          </div>
        )}

        {latestScan && (
          <div className="sensor-data-stats">
            <div className="sensor-stat-item">
              <div className="sensor-stat-label">Angle Min</div>
              <div className="sensor-stat-value">{((latestScan.angle_min || 0) * 180 / Math.PI).toFixed(1)}°</div>
            </div>
            <div className="sensor-stat-item">
              <div className="sensor-stat-label">Angle Max</div>
              <div className="sensor-stat-value">{((latestScan.angle_max || 0) * 180 / Math.PI).toFixed(1)}°</div>
            </div>
            <div className="sensor-stat-item">
              <div className="sensor-stat-label">Increment</div>
              <div className="sensor-stat-value">{((latestScan.angle_increment || 0) * 180 / Math.PI).toFixed(2)}°</div>
            </div>
            <div className="sensor-stat-item">
              <div className="sensor-stat-label">Total Points</div>
              <div className="sensor-stat-value">{latestScan.ranges?.length || 0}</div>
            </div>
          </div>
        )}
      </div>

      <div className="sensor-status">
        <div className="sensor-topic-info">
          Topic: /scan | Scans: {sensorData.length}
        </div>
        <div className={`sensor-connection-status ${isConnected ? 'sensor-connected' : 'sensor-disconnected'}`}>
          {isConnected ? 'Connected' : 'Disconnected'}
        </div>
      </div>
    </div>
  );
};

export default SensorDataDisplay;
