{"ast": null, "code": "var _jsxFileName = \"F:\\\\foxglove\\\\foxglove-panels\\\\src\\\\components\\\\RobotDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\n\n// Configuration - Change these URLs to match your setup\n// Option 1: ROSBridge WebSocket (default)\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ROS_WEBSOCKET_URL = 'ws://192.168.1.192:9090';\n// Option 2: Foxglove Bridge WebSocket (uncomment line below and comment line above)\n// const ROS_WEBSOCKET_URL = 'ws://192.168.1.192:8765';\n\nconst RobotDashboard = () => {\n  _s();\n  var _sensorData$, _sensorData$$ranges, _sensorData$2, _sensorData$2$range_m;\n  const [robotData, setRobotData] = useState({\n    position: {\n      x: 0,\n      y: 0,\n      z: 0\n    },\n    orientation: {\n      x: 0,\n      y: 0,\n      z: 0,\n      w: 1\n    },\n    velocity: {\n      linear: 0,\n      angular: 0\n    },\n    battery: 85,\n    connected: false\n  });\n  const [plotData, setPlotData] = useState({\n    x: [],\n    y: [],\n    timestamps: []\n  });\n  const [tfMessages, setTfMessages] = useState([]);\n  const [sensorData, setSensorData] = useState([]);\n  const [cameraImage, setCameraImage] = useState(null);\n  const mapCanvasRef = useRef(null);\n\n  // Connect to ROS2 via ROSBridge WebSocket\n  useEffect(() => {\n    const wsUrl = ROS_WEBSOCKET_URL;\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('Connected to ROS2 WebSocket');\n      setRobotData(prev => ({\n        ...prev,\n        connected: true\n      }));\n\n      // Subscribe to odometry topic\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/odom',\n        type: 'nav_msgs/Odometry'\n      }));\n\n      // Subscribe to battery state (if available)\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/battery_state',\n        type: 'sensor_msgs/BatteryState'\n      }));\n\n      // Subscribe to cmd_vel to monitor velocity commands\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/cmd_vel',\n        type: 'geometry_msgs/Twist'\n      }));\n\n      // Subscribe to tf for transform data (like Webviz)\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/tf',\n        type: 'tf2_msgs/TFMessage'\n      }));\n\n      // Subscribe to scan data for 3D visualization\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/scan',\n        type: 'sensor_msgs/LaserScan'\n      }));\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n        if (message.topic === '/odom' && message.msg) {\n          const pose = message.msg.pose.pose;\n          const twist = message.msg.twist.twist;\n          setRobotData(prev => ({\n            ...prev,\n            position: {\n              x: pose.position.x,\n              y: pose.position.y,\n              z: pose.position.z\n            },\n            orientation: {\n              x: pose.orientation.x,\n              y: pose.orientation.y,\n              z: pose.orientation.z,\n              w: pose.orientation.w\n            },\n            velocity: {\n              linear: Math.sqrt(twist.linear.x ** 2 + twist.linear.y ** 2),\n              angular: twist.angular.z\n            }\n          }));\n\n          // Add to plot data (like Webviz plot panel)\n          const timestamp = Date.now();\n          setPlotData(prev => ({\n            x: [...prev.x.slice(-50), pose.position.x],\n            // Keep last 50 points\n            y: [...prev.y.slice(-50), pose.position.y],\n            timestamps: [...prev.timestamps.slice(-50), timestamp]\n          }));\n        }\n        if (message.topic === '/tf' && message.msg) {\n          // Store TF messages (like Webviz Raw Messages panel)\n          setTfMessages(prev => [message.msg, ...prev.slice(0, 9)]); // Keep last 10 messages\n        }\n        if (message.topic === '/scan' && message.msg) {\n          // Store sensor data for 3D visualization\n          setSensorData(prev => [message.msg, ...prev.slice(0, 4)]); // Keep last 5 scans\n        }\n        if (message.topic === '/battery_state' && message.msg) {\n          setRobotData(prev => ({\n            ...prev,\n            battery: message.msg.percentage * 100\n          }));\n        }\n      } catch (error) {\n        console.error('Error parsing WebSocket message:', error);\n      }\n    };\n    ws.onclose = () => {\n      console.log('Disconnected from ROS2 WebSocket');\n      setRobotData(prev => ({\n        ...prev,\n        connected: false\n      }));\n    };\n    ws.onerror = error => {\n      console.error('WebSocket error:', error);\n      setRobotData(prev => ({\n        ...prev,\n        connected: false\n      }));\n    };\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n  // Connect to camera feed\n  useEffect(() => {\n    // Connect to camera topic via WebSocket\n    const wsUrl = ROS_WEBSOCKET_URL;\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      // Subscribe to camera topic\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/camera/image_raw',\n        type: 'sensor_msgs/Image'\n      }));\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n        if (message.topic === '/camera/image_raw' && message.msg) {\n          // Convert ROS image message to displayable format\n          const imageMsg = message.msg;\n          if (imageMsg.encoding === 'rgb8' || imageMsg.encoding === 'bgr8') {\n            // Create canvas to display image\n            const canvas = document.createElement('canvas');\n            canvas.width = imageMsg.width;\n            canvas.height = imageMsg.height;\n            const ctx = canvas.getContext('2d');\n            if (ctx) {\n              // Convert base64 data to image\n              const imageData = ctx.createImageData(imageMsg.width, imageMsg.height);\n              const data = atob(imageMsg.data);\n              for (let i = 0; i < data.length; i += 3) {\n                const pixelIndex = i / 3 * 4;\n                if (imageMsg.encoding === 'rgb8') {\n                  imageData.data[pixelIndex] = data.charCodeAt(i); // R\n                  imageData.data[pixelIndex + 1] = data.charCodeAt(i + 1); // G\n                  imageData.data[pixelIndex + 2] = data.charCodeAt(i + 2); // B\n                } else {\n                  // bgr8\n                  imageData.data[pixelIndex] = data.charCodeAt(i + 2); // R\n                  imageData.data[pixelIndex + 1] = data.charCodeAt(i + 1); // G\n                  imageData.data[pixelIndex + 2] = data.charCodeAt(i); // B\n                }\n                imageData.data[pixelIndex + 3] = 255; // A\n              }\n              ctx.putImageData(imageData, 0, 0);\n              setCameraImage(canvas.toDataURL());\n            }\n          }\n        }\n      } catch (error) {\n        console.error('Error processing camera message:', error);\n      }\n    };\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n  // Connect to map data and draw map with robot position\n  useEffect(() => {\n    const wsUrl = ROS_WEBSOCKET_URL;\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      // Subscribe to map topic\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/map',\n        type: 'nav_msgs/OccupancyGrid'\n      }));\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n        if (message.topic === '/map' && message.msg && mapCanvasRef.current) {\n          const mapMsg = message.msg;\n          const canvas = mapCanvasRef.current;\n          const ctx = canvas.getContext('2d');\n          if (ctx) {\n            canvas.width = mapMsg.info.width;\n            canvas.height = mapMsg.info.height;\n            const imageData = ctx.createImageData(mapMsg.info.width, mapMsg.info.height);\n\n            // Convert occupancy grid to image\n            for (let i = 0; i < mapMsg.data.length; i++) {\n              const value = mapMsg.data[i];\n              const pixelIndex = i * 4;\n              if (value === -1) {\n                // Unknown space - gray\n                imageData.data[pixelIndex] = 128; // R\n                imageData.data[pixelIndex + 1] = 128; // G\n                imageData.data[pixelIndex + 2] = 128; // B\n              } else if (value === 0) {\n                // Free space - white\n                imageData.data[pixelIndex] = 255; // R\n                imageData.data[pixelIndex + 1] = 255; // G\n                imageData.data[pixelIndex + 2] = 255; // B\n              } else {\n                // Occupied space - black\n                imageData.data[pixelIndex] = 0; // R\n                imageData.data[pixelIndex + 1] = 0; // G\n                imageData.data[pixelIndex + 2] = 0; // B\n              }\n              imageData.data[pixelIndex + 3] = 255; // A\n            }\n            ctx.putImageData(imageData, 0, 0);\n          }\n        }\n      } catch (error) {\n        console.error('Error processing map message:', error);\n      }\n    };\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n  // Draw robot position on map\n  useEffect(() => {\n    if (mapCanvasRef.current && robotData.connected) {\n      const canvas = mapCanvasRef.current;\n      const ctx = canvas.getContext('2d');\n      if (ctx) {\n        // Convert robot position to map coordinates\n        // This assumes map origin is at (0,0) - adjust based on your map setup\n        const robotX = robotData.position.x * 20 + canvas.width / 2; // Scale and center\n        const robotY = canvas.height - (robotData.position.y * 20 + canvas.height / 2); // Flip Y and center\n\n        // Draw robot as red circle\n        ctx.fillStyle = '#ff4444';\n        ctx.beginPath();\n        ctx.arc(robotX, robotY, 5, 0, 2 * Math.PI);\n        ctx.fill();\n\n        // Draw robot orientation arrow\n        const angle = Math.atan2(2 * (robotData.orientation.w * robotData.orientation.z + robotData.orientation.x * robotData.orientation.y), 1 - 2 * (robotData.orientation.y * robotData.orientation.y + robotData.orientation.z * robotData.orientation.z));\n        ctx.strokeStyle = '#fff';\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.moveTo(robotX, robotY);\n        ctx.lineTo(robotX + Math.cos(angle) * 15, robotY - Math.sin(angle) * 15 // Negative because canvas Y is flipped\n        );\n        ctx.stroke();\n      }\n    }\n  }, [robotData.position, robotData.orientation, robotData.connected]);\n  const getBatteryColor = level => {\n    if (level > 50) return '#4CAF50';\n    if (level > 20) return '#FF9800';\n    return '#F44336';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: '#0d1117',\n      minHeight: '100vh',\n      color: 'white',\n      fontFamily: 'monospace',\n      padding: '20px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        marginBottom: '30px',\n        borderBottom: '2px solid #333',\n        paddingBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          color: '#00bcd4',\n          margin: '0 0 10px 0'\n        },\n        children: \"\\uD83E\\uDD16 TurtleBot3 Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: '10px',\n          fontSize: '14px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: robotData.connected ? '#4CAF50' : '#F44336'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: robotData.connected ? 'CONNECTED TO ROS2' : 'DISCONNECTED'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            marginLeft: '20px',\n            fontSize: '12px',\n            color: '#666'\n          },\n          children: \"WebSocket: ws://localhost:9090 (ROSBridge)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n        gap: '20px',\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#00bcd4',\n            marginTop: 0\n          },\n          children: \"\\uD83D\\uDCC8 Position Plot (/odom \\u2192 pose.pose.position.x)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '200px',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444',\n            position: 'relative',\n            overflow: 'hidden'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n            ref: canvas => {\n              if (canvas && plotData.x.length > 0) {\n                const ctx = canvas.getContext('2d');\n                if (ctx) {\n                  canvas.width = canvas.offsetWidth;\n                  canvas.height = canvas.offsetHeight;\n                  ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n                  // Draw grid\n                  ctx.strokeStyle = '#333';\n                  ctx.lineWidth = 1;\n                  for (let i = 0; i < canvas.width; i += 40) {\n                    ctx.beginPath();\n                    ctx.moveTo(i, 0);\n                    ctx.lineTo(i, canvas.height);\n                    ctx.stroke();\n                  }\n                  for (let i = 0; i < canvas.height; i += 40) {\n                    ctx.beginPath();\n                    ctx.moveTo(0, i);\n                    ctx.lineTo(canvas.width, i);\n                    ctx.stroke();\n                  }\n\n                  // Draw plot line\n                  if (plotData.x.length > 1) {\n                    ctx.strokeStyle = '#00bcd4';\n                    ctx.lineWidth = 2;\n                    ctx.beginPath();\n                    const minX = Math.min(...plotData.x);\n                    const maxX = Math.max(...plotData.x);\n                    const range = maxX - minX || 1;\n                    plotData.x.forEach((x, i) => {\n                      const canvasX = i / (plotData.x.length - 1) * canvas.width;\n                      const canvasY = canvas.height - (x - minX) / range * canvas.height;\n                      if (i === 0) {\n                        ctx.moveTo(canvasX, canvasY);\n                      } else {\n                        ctx.lineTo(canvasX, canvasY);\n                      }\n                    });\n                    ctx.stroke();\n                  }\n                }\n              }\n            },\n            style: {\n              width: '100%',\n              height: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#aaa',\n            marginTop: '10px'\n          },\n          children: [\"Current X: \", robotData.position.x.toFixed(3), \"m | Points: \", plotData.x.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#FF9800',\n            marginTop: 0\n          },\n          children: \"\\uD83D\\uDCCB Raw Messages (/tf)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '200px',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444',\n            padding: '10px',\n            overflow: 'auto',\n            fontFamily: 'monospace',\n            fontSize: '11px'\n          },\n          children: tfMessages.length > 0 ? tfMessages.map((msg, index) => {\n            var _msg$transforms, _msg$transforms$, _msg$transforms$$head, _msg$transforms2, _msg$transforms2$, _msg$transforms3, _msg$transforms3$, _msg$transforms$0$tra, _msg$transforms$0$tra2, _msg$transforms$0$tra3;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '8px',\n                color: index === 0 ? '#00ff00' : '#aaa',\n                borderBottom: '1px solid #333',\n                paddingBottom: '4px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#00bcd4'\n                },\n                children: [\"Transform #\", index + 1, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Frames: \", ((_msg$transforms = msg.transforms) === null || _msg$transforms === void 0 ? void 0 : (_msg$transforms$ = _msg$transforms[0]) === null || _msg$transforms$ === void 0 ? void 0 : (_msg$transforms$$head = _msg$transforms$.header) === null || _msg$transforms$$head === void 0 ? void 0 : _msg$transforms$$head.frame_id) || 'N/A', \" \\u2192 \", ((_msg$transforms2 = msg.transforms) === null || _msg$transforms2 === void 0 ? void 0 : (_msg$transforms2$ = _msg$transforms2[0]) === null || _msg$transforms2$ === void 0 ? void 0 : _msg$transforms2$.child_frame_id) || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 19\n              }, this), ((_msg$transforms3 = msg.transforms) === null || _msg$transforms3 === void 0 ? void 0 : (_msg$transforms3$ = _msg$transforms3[0]) === null || _msg$transforms3$ === void 0 ? void 0 : _msg$transforms3$.transform) && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Translation: [\", (_msg$transforms$0$tra = msg.transforms[0].transform.translation.x) === null || _msg$transforms$0$tra === void 0 ? void 0 : _msg$transforms$0$tra.toFixed(3), \", \", (_msg$transforms$0$tra2 = msg.transforms[0].transform.translation.y) === null || _msg$transforms$0$tra2 === void 0 ? void 0 : _msg$transforms$0$tra2.toFixed(3), \", \", (_msg$transforms$0$tra3 = msg.transforms[0].transform.translation.z) === null || _msg$transforms$0$tra3 === void 0 ? void 0 : _msg$transforms$0$tra3.toFixed(3), \"]\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 17\n            }, this);\n          }) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#666',\n              textAlign: 'center',\n              marginTop: '80px'\n            },\n            children: \"Waiting for /tf messages...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#aaa',\n            marginTop: '10px'\n          },\n          children: [\"Messages received: \", tfMessages.length, \" | Topic: /tf\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#4CAF50',\n            marginTop: 0\n          },\n          children: \"\\uD83D\\uDCCA Robot Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '5px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Position:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              color: '#aaa'\n            },\n            children: [\"X: \", robotData.position.x.toFixed(2), \"m | Y: \", robotData.position.y.toFixed(2), \"m | Z: \", robotData.position.z.toFixed(2), \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '5px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Velocity:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              color: '#aaa'\n            },\n            children: [\"Linear: \", robotData.velocity.linear.toFixed(2), \"m/s | Angular: \", robotData.velocity.angular.toFixed(2), \"rad/s\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '5px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Battery:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: getBatteryColor(robotData.battery)\n              },\n              children: [robotData.battery.toFixed(1), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100%',\n              height: '8px',\n              backgroundColor: '#333',\n              borderRadius: '4px',\n              overflow: 'hidden'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: `${robotData.battery}%`,\n                height: '100%',\n                backgroundColor: getBatteryColor(robotData.battery),\n                transition: 'width 0.3s ease'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#FF9800',\n            marginTop: 0\n          },\n          children: \"\\uD83D\\uDCF7 Camera Feed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '200px',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444'\n          },\n          children: cameraImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: cameraImage,\n            alt: \"Robot Camera\",\n            style: {\n              maxWidth: '100%',\n              maxHeight: '200px',\n              borderRadius: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#666',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '2rem',\n                marginBottom: '10px'\n              },\n              children: \"\\uD83D\\uDCF7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"No camera feed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#aaa',\n            marginTop: '10px',\n            textAlign: 'center'\n          },\n          children: \"Topic: /camera/image_raw | 10 FPS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 559,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#E91E63',\n            marginTop: 0\n          },\n          children: \"\\uD83C\\uDF10 3D Sensor View (/scan)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '200px',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444',\n            position: 'relative',\n            overflow: 'hidden'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n            ref: canvas => {\n              if (canvas && sensorData.length > 0) {\n                const ctx = canvas.getContext('2d');\n                if (ctx) {\n                  canvas.width = canvas.offsetWidth;\n                  canvas.height = canvas.offsetHeight;\n                  ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n                  // Draw coordinate system\n                  const centerX = canvas.width / 2;\n                  const centerY = canvas.height / 2;\n\n                  // Draw axes\n                  ctx.strokeStyle = '#333';\n                  ctx.lineWidth = 1;\n                  ctx.beginPath();\n                  ctx.moveTo(0, centerY);\n                  ctx.lineTo(canvas.width, centerY);\n                  ctx.moveTo(centerX, 0);\n                  ctx.lineTo(centerX, canvas.height);\n                  ctx.stroke();\n\n                  // Draw laser scan points\n                  const scan = sensorData[0];\n                  if (scan && scan.ranges) {\n                    ctx.fillStyle = '#00ff00';\n                    const angleMin = scan.angle_min || -Math.PI;\n                    const angleIncrement = scan.angle_increment || 2 * Math.PI / scan.ranges.length;\n                    const scale = 50; // Scale factor for visualization\n\n                    scan.ranges.forEach((range, i) => {\n                      if (range > scan.range_min && range < scan.range_max) {\n                        const angle = angleMin + i * angleIncrement;\n                        const x = centerX + range * Math.cos(angle) * scale;\n                        const y = centerY - range * Math.sin(angle) * scale;\n                        if (x >= 0 && x < canvas.width && y >= 0 && y < canvas.height) {\n                          ctx.beginPath();\n                          ctx.arc(x, y, 1, 0, 2 * Math.PI);\n                          ctx.fill();\n                        }\n                      }\n                    });\n\n                    // Draw robot position\n                    ctx.fillStyle = '#ff0000';\n                    ctx.beginPath();\n                    ctx.arc(centerX, centerY, 3, 0, 2 * Math.PI);\n                    ctx.fill();\n                  }\n                }\n              }\n            },\n            style: {\n              width: '100%',\n              height: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#aaa',\n            marginTop: '10px'\n          },\n          children: [\"Laser scan points: \", ((_sensorData$ = sensorData[0]) === null || _sensorData$ === void 0 ? void 0 : (_sensorData$$ranges = _sensorData$.ranges) === null || _sensorData$$ranges === void 0 ? void 0 : _sensorData$$ranges.length) || 0, \" | Range: \", ((_sensorData$2 = sensorData[0]) === null || _sensorData$2 === void 0 ? void 0 : (_sensorData$2$range_m = _sensorData$2.range_max) === null || _sensorData$2$range_m === void 0 ? void 0 : _sensorData$2$range_m.toFixed(1)) || 'N/A', \"m\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 676,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 603,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#9C27B0',\n            marginTop: 0\n          },\n          children: \"\\uD83D\\uDDFA\\uFE0F Map & Navigation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 688,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n            ref: mapCanvasRef,\n            style: {\n              maxWidth: '100%',\n              borderRadius: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 697,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 689,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#aaa',\n            marginTop: '10px',\n            textAlign: 'center'\n          },\n          children: \"Topic: /map | Robot position tracked\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 705,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 682,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '20px',\n        borderTop: '1px solid #333',\n        color: '#666',\n        fontSize: '12px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\uD83C\\uDF10 TurtleBot3 Webviz-Style Dashboard via ROSBridge\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 724,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '5px'\n        },\n        children: [\"Topics: /odom (plot), /tf (raw messages), /scan (3D), /camera/image_raw, /map | WebSocket: \", ROS_WEBSOCKET_URL]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 725,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 717,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 333,\n    columnNumber: 5\n  }, this);\n};\n_s(RobotDashboard, \"XNOvuGGB+mr2iwIBThi5iXK7s6M=\");\n_c = RobotDashboard;\nexport default RobotDashboard;\nvar _c;\n$RefreshReg$(_c, \"RobotDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "ROS_WEBSOCKET_URL", "RobotDashboard", "_s", "_sensorData$", "_sensorData$$ranges", "_sensorData$2", "_sensorData$2$range_m", "robotData", "setRobotData", "position", "x", "y", "z", "orientation", "w", "velocity", "linear", "angular", "battery", "connected", "plotData", "setPlotData", "timestamps", "tfMessages", "setTfMessages", "sensorData", "setSensorData", "cameraImage", "setCameraImage", "mapCanvasRef", "wsUrl", "ws", "WebSocket", "onopen", "console", "log", "prev", "send", "JSON", "stringify", "op", "topic", "type", "onmessage", "event", "message", "parse", "data", "msg", "pose", "twist", "Math", "sqrt", "timestamp", "Date", "now", "slice", "percentage", "error", "onclose", "onerror", "close", "imageMsg", "encoding", "canvas", "document", "createElement", "width", "height", "ctx", "getContext", "imageData", "createImageData", "atob", "i", "length", "pixelIndex", "charCodeAt", "putImageData", "toDataURL", "current", "mapMsg", "info", "value", "robotX", "robotY", "fillStyle", "beginPath", "arc", "PI", "fill", "angle", "atan2", "strokeStyle", "lineWidth", "moveTo", "lineTo", "cos", "sin", "stroke", "getBatteryColor", "level", "style", "background", "minHeight", "color", "fontFamily", "padding", "children", "textAlign", "marginBottom", "borderBottom", "paddingBottom", "margin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "alignItems", "justifyContent", "gap", "fontSize", "borderRadius", "backgroundColor", "marginLeft", "gridTemplateColumns", "border", "marginTop", "overflow", "ref", "offsetWidth", "offsetHeight", "clearRect", "minX", "min", "maxX", "max", "range", "for<PERSON>ach", "canvasX", "canvasY", "toFixed", "map", "index", "_msg$transforms", "_msg$transforms$", "_msg$transforms$$head", "_msg$transforms2", "_msg$transforms2$", "_msg$transforms3", "_msg$transforms3$", "_msg$transforms$0$tra", "_msg$transforms$0$tra2", "_msg$transforms$0$tra3", "transforms", "header", "frame_id", "child_frame_id", "transform", "translation", "transition", "src", "alt", "max<PERSON><PERSON><PERSON>", "maxHeight", "centerX", "centerY", "scan", "ranges", "angleMin", "angle_min", "angleIncrement", "angle_increment", "scale", "range_min", "range_max", "borderTop", "_c", "$RefreshReg$"], "sources": ["F:/foxglove/foxglove-panels/src/components/RobotDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\n\n// Configuration - Change these URLs to match your setup\n// Option 1: ROSBridge WebSocket (default)\nconst ROS_WEBSOCKET_URL = 'ws://192.168.1.192:9090';\n// Option 2: Foxglove Bridge WebSocket (uncomment line below and comment line above)\n// const ROS_WEBSOCKET_URL = 'ws://192.168.1.192:8765';\n\ninterface RobotData {\n  position: { x: number; y: number; z: number };\n  orientation: { x: number; y: number; z: number; w: number };\n  velocity: { linear: number; angular: number };\n  battery: number;\n  connected: boolean;\n}\n\n\n\nconst RobotDashboard: React.FC = () => {\n  const [robotData, setRobotData] = useState<RobotData>({\n    position: { x: 0, y: 0, z: 0 },\n    orientation: { x: 0, y: 0, z: 0, w: 1 },\n    velocity: { linear: 0, angular: 0 },\n    battery: 85,\n    connected: false\n  });\n\n  const [plotData, setPlotData] = useState<{x: number[], y: number[], timestamps: number[]}>({\n    x: [],\n    y: [],\n    timestamps: []\n  });\n\n  const [tfMessages, setTfMessages] = useState<any[]>([]);\n  const [sensorData, setSensorData] = useState<any[]>([]);\n\n  const [cameraImage, setCameraImage] = useState<string | null>(null);\n  const mapCanvasRef = useRef<HTMLCanvasElement>(null);\n\n  // Connect to ROS2 via ROSBridge WebSocket\n  useEffect(() => {\n    const wsUrl = ROS_WEBSOCKET_URL;\n\n    const ws = new WebSocket(wsUrl);\n\n    ws.onopen = () => {\n      console.log('Connected to ROS2 WebSocket');\n      setRobotData(prev => ({ ...prev, connected: true }));\n\n      // Subscribe to odometry topic\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/odom',\n        type: 'nav_msgs/Odometry'\n      }));\n\n      // Subscribe to battery state (if available)\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/battery_state',\n        type: 'sensor_msgs/BatteryState'\n      }));\n\n      // Subscribe to cmd_vel to monitor velocity commands\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/cmd_vel',\n        type: 'geometry_msgs/Twist'\n      }));\n\n      // Subscribe to tf for transform data (like Webviz)\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/tf',\n        type: 'tf2_msgs/TFMessage'\n      }));\n\n      // Subscribe to scan data for 3D visualization\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/scan',\n        type: 'sensor_msgs/LaserScan'\n      }));\n    };\n\n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n\n        if (message.topic === '/odom' && message.msg) {\n          const pose = message.msg.pose.pose;\n          const twist = message.msg.twist.twist;\n\n          setRobotData(prev => ({\n            ...prev,\n            position: {\n              x: pose.position.x,\n              y: pose.position.y,\n              z: pose.position.z\n            },\n            orientation: {\n              x: pose.orientation.x,\n              y: pose.orientation.y,\n              z: pose.orientation.z,\n              w: pose.orientation.w\n            },\n            velocity: {\n              linear: Math.sqrt(twist.linear.x ** 2 + twist.linear.y ** 2),\n              angular: twist.angular.z\n            }\n          }));\n\n          // Add to plot data (like Webviz plot panel)\n          const timestamp = Date.now();\n          setPlotData(prev => ({\n            x: [...prev.x.slice(-50), pose.position.x], // Keep last 50 points\n            y: [...prev.y.slice(-50), pose.position.y],\n            timestamps: [...prev.timestamps.slice(-50), timestamp]\n          }));\n        }\n\n        if (message.topic === '/tf' && message.msg) {\n          // Store TF messages (like Webviz Raw Messages panel)\n          setTfMessages(prev => [message.msg, ...prev.slice(0, 9)]); // Keep last 10 messages\n        }\n\n        if (message.topic === '/scan' && message.msg) {\n          // Store sensor data for 3D visualization\n          setSensorData(prev => [message.msg, ...prev.slice(0, 4)]); // Keep last 5 scans\n        }\n\n        if (message.topic === '/battery_state' && message.msg) {\n          setRobotData(prev => ({\n            ...prev,\n            battery: message.msg.percentage * 100\n          }));\n        }\n\n      } catch (error) {\n        console.error('Error parsing WebSocket message:', error);\n      }\n    };\n\n    ws.onclose = () => {\n      console.log('Disconnected from ROS2 WebSocket');\n      setRobotData(prev => ({ ...prev, connected: false }));\n    };\n\n    ws.onerror = (error) => {\n      console.error('WebSocket error:', error);\n      setRobotData(prev => ({ ...prev, connected: false }));\n    };\n\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n  // Connect to camera feed\n  useEffect(() => {\n    // Connect to camera topic via WebSocket\n    const wsUrl = ROS_WEBSOCKET_URL;\n    const ws = new WebSocket(wsUrl);\n\n    ws.onopen = () => {\n      // Subscribe to camera topic\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/camera/image_raw',\n        type: 'sensor_msgs/Image'\n      }));\n    };\n\n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n\n        if (message.topic === '/camera/image_raw' && message.msg) {\n          // Convert ROS image message to displayable format\n          const imageMsg = message.msg;\n\n          if (imageMsg.encoding === 'rgb8' || imageMsg.encoding === 'bgr8') {\n            // Create canvas to display image\n            const canvas = document.createElement('canvas');\n            canvas.width = imageMsg.width;\n            canvas.height = imageMsg.height;\n            const ctx = canvas.getContext('2d');\n\n            if (ctx) {\n              // Convert base64 data to image\n              const imageData = ctx.createImageData(imageMsg.width, imageMsg.height);\n              const data = atob(imageMsg.data);\n\n              for (let i = 0; i < data.length; i += 3) {\n                const pixelIndex = (i / 3) * 4;\n                if (imageMsg.encoding === 'rgb8') {\n                  imageData.data[pixelIndex] = data.charCodeAt(i);     // R\n                  imageData.data[pixelIndex + 1] = data.charCodeAt(i + 1); // G\n                  imageData.data[pixelIndex + 2] = data.charCodeAt(i + 2); // B\n                } else { // bgr8\n                  imageData.data[pixelIndex] = data.charCodeAt(i + 2);     // R\n                  imageData.data[pixelIndex + 1] = data.charCodeAt(i + 1); // G\n                  imageData.data[pixelIndex + 2] = data.charCodeAt(i);     // B\n                }\n                imageData.data[pixelIndex + 3] = 255; // A\n              }\n\n              ctx.putImageData(imageData, 0, 0);\n              setCameraImage(canvas.toDataURL());\n            }\n          }\n        }\n      } catch (error) {\n        console.error('Error processing camera message:', error);\n      }\n    };\n\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n  // Connect to map data and draw map with robot position\n  useEffect(() => {\n    const wsUrl = ROS_WEBSOCKET_URL;\n    const ws = new WebSocket(wsUrl);\n\n    ws.onopen = () => {\n      // Subscribe to map topic\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/map',\n        type: 'nav_msgs/OccupancyGrid'\n      }));\n    };\n\n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n\n        if (message.topic === '/map' && message.msg && mapCanvasRef.current) {\n          const mapMsg = message.msg;\n          const canvas = mapCanvasRef.current;\n          const ctx = canvas.getContext('2d');\n\n          if (ctx) {\n            canvas.width = mapMsg.info.width;\n            canvas.height = mapMsg.info.height;\n\n            const imageData = ctx.createImageData(mapMsg.info.width, mapMsg.info.height);\n\n            // Convert occupancy grid to image\n            for (let i = 0; i < mapMsg.data.length; i++) {\n              const value = mapMsg.data[i];\n              const pixelIndex = i * 4;\n\n              if (value === -1) {\n                // Unknown space - gray\n                imageData.data[pixelIndex] = 128;     // R\n                imageData.data[pixelIndex + 1] = 128; // G\n                imageData.data[pixelIndex + 2] = 128; // B\n              } else if (value === 0) {\n                // Free space - white\n                imageData.data[pixelIndex] = 255;     // R\n                imageData.data[pixelIndex + 1] = 255; // G\n                imageData.data[pixelIndex + 2] = 255; // B\n              } else {\n                // Occupied space - black\n                imageData.data[pixelIndex] = 0;       // R\n                imageData.data[pixelIndex + 1] = 0;   // G\n                imageData.data[pixelIndex + 2] = 0;   // B\n              }\n              imageData.data[pixelIndex + 3] = 255; // A\n            }\n\n            ctx.putImageData(imageData, 0, 0);\n          }\n        }\n      } catch (error) {\n        console.error('Error processing map message:', error);\n      }\n    };\n\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n  // Draw robot position on map\n  useEffect(() => {\n    if (mapCanvasRef.current && robotData.connected) {\n      const canvas = mapCanvasRef.current;\n      const ctx = canvas.getContext('2d');\n\n      if (ctx) {\n        // Convert robot position to map coordinates\n        // This assumes map origin is at (0,0) - adjust based on your map setup\n        const robotX = (robotData.position.x * 20) + canvas.width / 2;  // Scale and center\n        const robotY = canvas.height - ((robotData.position.y * 20) + canvas.height / 2); // Flip Y and center\n\n        // Draw robot as red circle\n        ctx.fillStyle = '#ff4444';\n        ctx.beginPath();\n        ctx.arc(robotX, robotY, 5, 0, 2 * Math.PI);\n        ctx.fill();\n\n        // Draw robot orientation arrow\n        const angle = Math.atan2(\n          2 * (robotData.orientation.w * robotData.orientation.z + robotData.orientation.x * robotData.orientation.y),\n          1 - 2 * (robotData.orientation.y * robotData.orientation.y + robotData.orientation.z * robotData.orientation.z)\n        );\n\n        ctx.strokeStyle = '#fff';\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.moveTo(robotX, robotY);\n        ctx.lineTo(\n          robotX + Math.cos(angle) * 15,\n          robotY - Math.sin(angle) * 15  // Negative because canvas Y is flipped\n        );\n        ctx.stroke();\n      }\n    }\n  }, [robotData.position, robotData.orientation, robotData.connected]);\n\n  const getBatteryColor = (level: number): string => {\n    if (level > 50) return '#4CAF50';\n    if (level > 20) return '#FF9800';\n    return '#F44336';\n  };\n\n  return (\n    <div style={{\n      background: '#0d1117',\n      minHeight: '100vh',\n      color: 'white',\n      fontFamily: 'monospace',\n      padding: '20px'\n    }}>\n      {/* Header */}\n      <div style={{\n        textAlign: 'center',\n        marginBottom: '30px',\n        borderBottom: '2px solid #333',\n        paddingBottom: '20px'\n      }}>\n        <h1 style={{ color: '#00bcd4', margin: '0 0 10px 0' }}>\n          🤖 TurtleBot3 Dashboard\n        </h1>\n        <div style={{ \n          display: 'flex', \n          alignItems: 'center', \n          justifyContent: 'center', \n          gap: '10px',\n          fontSize: '14px'\n        }}>\n          <div style={{\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: robotData.connected ? '#4CAF50' : '#F44336'\n          }}></div>\n          <span>{robotData.connected ? 'CONNECTED TO ROS2' : 'DISCONNECTED'}</span>\n          <span style={{ marginLeft: '20px', fontSize: '12px', color: '#666' }}>\n            WebSocket: ws://localhost:9090 (ROSBridge)\n          </span>\n        </div>\n      </div>\n\n      {/* Main Dashboard Grid - Webviz Style Layout */}\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n        gap: '20px',\n        marginBottom: '20px'\n      }}>\n\n        {/* Plot Panel - Position X over time (like Webviz) */}\n        <div style={{\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        }}>\n          <h3 style={{ color: '#00bcd4', marginTop: 0 }}>📈 Position Plot (/odom → pose.pose.position.x)</h3>\n          <div style={{\n            height: '200px',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444',\n            position: 'relative',\n            overflow: 'hidden'\n          }}>\n            <canvas\n              ref={(canvas) => {\n                if (canvas && plotData.x.length > 0) {\n                  const ctx = canvas.getContext('2d');\n                  if (ctx) {\n                    canvas.width = canvas.offsetWidth;\n                    canvas.height = canvas.offsetHeight;\n\n                    ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n                    // Draw grid\n                    ctx.strokeStyle = '#333';\n                    ctx.lineWidth = 1;\n                    for (let i = 0; i < canvas.width; i += 40) {\n                      ctx.beginPath();\n                      ctx.moveTo(i, 0);\n                      ctx.lineTo(i, canvas.height);\n                      ctx.stroke();\n                    }\n                    for (let i = 0; i < canvas.height; i += 40) {\n                      ctx.beginPath();\n                      ctx.moveTo(0, i);\n                      ctx.lineTo(canvas.width, i);\n                      ctx.stroke();\n                    }\n\n                    // Draw plot line\n                    if (plotData.x.length > 1) {\n                      ctx.strokeStyle = '#00bcd4';\n                      ctx.lineWidth = 2;\n                      ctx.beginPath();\n\n                      const minX = Math.min(...plotData.x);\n                      const maxX = Math.max(...plotData.x);\n                      const range = maxX - minX || 1;\n\n                      plotData.x.forEach((x, i) => {\n                        const canvasX = (i / (plotData.x.length - 1)) * canvas.width;\n                        const canvasY = canvas.height - ((x - minX) / range) * canvas.height;\n\n                        if (i === 0) {\n                          ctx.moveTo(canvasX, canvasY);\n                        } else {\n                          ctx.lineTo(canvasX, canvasY);\n                        }\n                      });\n\n                      ctx.stroke();\n                    }\n                  }\n                }\n              }}\n              style={{ width: '100%', height: '100%' }}\n            />\n          </div>\n          <div style={{ fontSize: '12px', color: '#aaa', marginTop: '10px' }}>\n            Current X: {robotData.position.x.toFixed(3)}m | Points: {plotData.x.length}\n          </div>\n        </div>\n        \n        {/* Raw Messages Panel - TF Messages (like Webviz) */}\n        <div style={{\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        }}>\n          <h3 style={{ color: '#FF9800', marginTop: 0 }}>📋 Raw Messages (/tf)</h3>\n          <div style={{\n            height: '200px',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444',\n            padding: '10px',\n            overflow: 'auto',\n            fontFamily: 'monospace',\n            fontSize: '11px'\n          }}>\n            {tfMessages.length > 0 ? (\n              tfMessages.map((msg, index) => (\n                <div key={index} style={{\n                  marginBottom: '8px',\n                  color: index === 0 ? '#00ff00' : '#aaa',\n                  borderBottom: '1px solid #333',\n                  paddingBottom: '4px'\n                }}>\n                  <div style={{ color: '#00bcd4' }}>\n                    Transform #{index + 1}:\n                  </div>\n                  <div>\n                    Frames: {msg.transforms?.[0]?.header?.frame_id || 'N/A'} → {msg.transforms?.[0]?.child_frame_id || 'N/A'}\n                  </div>\n                  {msg.transforms?.[0]?.transform && (\n                    <div>\n                      Translation: [{msg.transforms[0].transform.translation.x?.toFixed(3)}, {msg.transforms[0].transform.translation.y?.toFixed(3)}, {msg.transforms[0].transform.translation.z?.toFixed(3)}]\n                    </div>\n                  )}\n                </div>\n              ))\n            ) : (\n              <div style={{ color: '#666', textAlign: 'center', marginTop: '80px' }}>\n                Waiting for /tf messages...\n              </div>\n            )}\n          </div>\n          <div style={{ fontSize: '12px', color: '#aaa', marginTop: '10px' }}>\n            Messages received: {tfMessages.length} | Topic: /tf\n          </div>\n        </div>\n\n        {/* Robot Status */}\n        <div style={{\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        }}>\n          <h3 style={{ color: '#4CAF50', marginTop: 0 }}>📊 Robot Status</h3>\n          \n          <div style={{ marginBottom: '15px' }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\n              <span>Position:</span>\n            </div>\n            <div style={{ fontSize: '12px', color: '#aaa' }}>\n              X: {robotData.position.x.toFixed(2)}m | \n              Y: {robotData.position.y.toFixed(2)}m | \n              Z: {robotData.position.z.toFixed(2)}m\n            </div>\n          </div>\n\n          <div style={{ marginBottom: '15px' }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\n              <span>Velocity:</span>\n            </div>\n            <div style={{ fontSize: '12px', color: '#aaa' }}>\n              Linear: {robotData.velocity.linear.toFixed(2)}m/s | \n              Angular: {robotData.velocity.angular.toFixed(2)}rad/s\n            </div>\n          </div>\n\n          <div style={{ marginBottom: '15px' }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\n              <span>Battery:</span>\n              <span style={{ color: getBatteryColor(robotData.battery) }}>\n                {robotData.battery.toFixed(1)}%\n              </span>\n            </div>\n            <div style={{\n              width: '100%',\n              height: '8px',\n              backgroundColor: '#333',\n              borderRadius: '4px',\n              overflow: 'hidden'\n            }}>\n              <div style={{\n                width: `${robotData.battery}%`,\n                height: '100%',\n                backgroundColor: getBatteryColor(robotData.battery),\n                transition: 'width 0.3s ease'\n              }}></div>\n            </div>\n          </div>\n        </div>\n\n        {/* Camera Feed */}\n        <div style={{\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        }}>\n          <h3 style={{ color: '#FF9800', marginTop: 0 }}>📷 Camera Feed</h3>\n          <div style={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '200px',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444'\n          }}>\n            {cameraImage ? (\n              <img \n                src={cameraImage} \n                alt=\"Robot Camera\" \n                style={{ \n                  maxWidth: '100%', \n                  maxHeight: '200px',\n                  borderRadius: '4px'\n                }} \n              />\n            ) : (\n              <div style={{ color: '#666', textAlign: 'center' }}>\n                <div style={{ fontSize: '2rem', marginBottom: '10px' }}>📷</div>\n                <div>No camera feed</div>\n              </div>\n            )}\n          </div>\n          <div style={{ \n            fontSize: '12px', \n            color: '#aaa', \n            marginTop: '10px',\n            textAlign: 'center'\n          }}>\n            Topic: /camera/image_raw | 10 FPS\n          </div>\n        </div>\n\n        {/* 3D Sensor Visualization (like Webviz 3D Panel) */}\n        <div style={{\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        }}>\n          <h3 style={{ color: '#E91E63', marginTop: 0 }}>🌐 3D Sensor View (/scan)</h3>\n          <div style={{\n            height: '200px',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444',\n            position: 'relative',\n            overflow: 'hidden'\n          }}>\n            <canvas\n              ref={(canvas) => {\n                if (canvas && sensorData.length > 0) {\n                  const ctx = canvas.getContext('2d');\n                  if (ctx) {\n                    canvas.width = canvas.offsetWidth;\n                    canvas.height = canvas.offsetHeight;\n\n                    ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n                    // Draw coordinate system\n                    const centerX = canvas.width / 2;\n                    const centerY = canvas.height / 2;\n\n                    // Draw axes\n                    ctx.strokeStyle = '#333';\n                    ctx.lineWidth = 1;\n                    ctx.beginPath();\n                    ctx.moveTo(0, centerY);\n                    ctx.lineTo(canvas.width, centerY);\n                    ctx.moveTo(centerX, 0);\n                    ctx.lineTo(centerX, canvas.height);\n                    ctx.stroke();\n\n                    // Draw laser scan points\n                    const scan = sensorData[0];\n                    if (scan && scan.ranges) {\n                      ctx.fillStyle = '#00ff00';\n                      const angleMin = scan.angle_min || -Math.PI;\n                      const angleIncrement = scan.angle_increment || (2 * Math.PI / scan.ranges.length);\n                      const scale = 50; // Scale factor for visualization\n\n                      scan.ranges.forEach((range: number, i: number) => {\n                        if (range > scan.range_min && range < scan.range_max) {\n                          const angle = angleMin + i * angleIncrement;\n                          const x = centerX + range * Math.cos(angle) * scale;\n                          const y = centerY - range * Math.sin(angle) * scale;\n\n                          if (x >= 0 && x < canvas.width && y >= 0 && y < canvas.height) {\n                            ctx.beginPath();\n                            ctx.arc(x, y, 1, 0, 2 * Math.PI);\n                            ctx.fill();\n                          }\n                        }\n                      });\n\n                      // Draw robot position\n                      ctx.fillStyle = '#ff0000';\n                      ctx.beginPath();\n                      ctx.arc(centerX, centerY, 3, 0, 2 * Math.PI);\n                      ctx.fill();\n                    }\n                  }\n                }\n              }}\n              style={{ width: '100%', height: '100%' }}\n            />\n          </div>\n          <div style={{ fontSize: '12px', color: '#aaa', marginTop: '10px' }}>\n            Laser scan points: {sensorData[0]?.ranges?.length || 0} | Range: {sensorData[0]?.range_max?.toFixed(1) || 'N/A'}m\n          </div>\n        </div>\n\n        {/* Map Viewer */}\n        <div style={{\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        }}>\n          <h3 style={{ color: '#9C27B0', marginTop: 0 }}>🗺️ Map & Navigation</h3>\n          <div style={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444'\n          }}>\n            <canvas \n              ref={mapCanvasRef}\n              style={{ \n                maxWidth: '100%',\n                borderRadius: '4px'\n              }}\n            />\n          </div>\n          <div style={{ \n            fontSize: '12px', \n            color: '#aaa', \n            marginTop: '10px',\n            textAlign: 'center'\n          }}>\n            Topic: /map | Robot position tracked\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div style={{\n        textAlign: 'center',\n        padding: '20px',\n        borderTop: '1px solid #333',\n        color: '#666',\n        fontSize: '12px'\n      }}>\n        <div>🌐 TurtleBot3 Webviz-Style Dashboard via ROSBridge</div>\n        <div style={{ marginTop: '5px' }}>\n          Topics: /odom (plot), /tf (raw messages), /scan (3D), /camera/image_raw, /map | WebSocket: {ROS_WEBSOCKET_URL}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RobotDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;;AAE1D;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,iBAAiB,GAAG,yBAAyB;AACnD;AACA;;AAYA,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA,EAAAC,mBAAA,EAAAC,aAAA,EAAAC,qBAAA;EACrC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAY;IACpDc,QAAQ,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAC9BC,WAAW,EAAE;MAAEH,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC;IACvCC,QAAQ,EAAE;MAAEC,MAAM,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAE,CAAC;IACnCC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAmD;IACzFe,CAAC,EAAE,EAAE;IACLC,CAAC,EAAE,EAAE;IACLW,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAQ,EAAE,CAAC;EACvD,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAQ,EAAE,CAAC;EAEvD,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAgB,IAAI,CAAC;EACnE,MAAMkC,YAAY,GAAGhC,MAAM,CAAoB,IAAI,CAAC;;EAEpD;EACAD,SAAS,CAAC,MAAM;IACd,MAAMkC,KAAK,GAAG9B,iBAAiB;IAE/B,MAAM+B,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChBC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1C3B,YAAY,CAAC4B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEjB,SAAS,EAAE;MAAK,CAAC,CAAC,CAAC;;MAEpD;MACAY,EAAE,CAACM,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QACrBC,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;;MAEH;MACAX,EAAE,CAACM,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QACrBC,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;;MAEH;MACAX,EAAE,CAACM,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QACrBC,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,UAAU;QACjBC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;;MAEH;MACAX,EAAE,CAACM,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QACrBC,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;;MAEH;MACAX,EAAE,CAACM,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QACrBC,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;IACL,CAAC;IAEDX,EAAE,CAACY,SAAS,GAAIC,KAAK,IAAK;MACxB,IAAI;QACF,MAAMC,OAAO,GAAGP,IAAI,CAACQ,KAAK,CAACF,KAAK,CAACG,IAAI,CAAC;QAEtC,IAAIF,OAAO,CAACJ,KAAK,KAAK,OAAO,IAAII,OAAO,CAACG,GAAG,EAAE;UAC5C,MAAMC,IAAI,GAAGJ,OAAO,CAACG,GAAG,CAACC,IAAI,CAACA,IAAI;UAClC,MAAMC,KAAK,GAAGL,OAAO,CAACG,GAAG,CAACE,KAAK,CAACA,KAAK;UAErC1C,YAAY,CAAC4B,IAAI,KAAK;YACpB,GAAGA,IAAI;YACP3B,QAAQ,EAAE;cACRC,CAAC,EAAEuC,IAAI,CAACxC,QAAQ,CAACC,CAAC;cAClBC,CAAC,EAAEsC,IAAI,CAACxC,QAAQ,CAACE,CAAC;cAClBC,CAAC,EAAEqC,IAAI,CAACxC,QAAQ,CAACG;YACnB,CAAC;YACDC,WAAW,EAAE;cACXH,CAAC,EAAEuC,IAAI,CAACpC,WAAW,CAACH,CAAC;cACrBC,CAAC,EAAEsC,IAAI,CAACpC,WAAW,CAACF,CAAC;cACrBC,CAAC,EAAEqC,IAAI,CAACpC,WAAW,CAACD,CAAC;cACrBE,CAAC,EAAEmC,IAAI,CAACpC,WAAW,CAACC;YACtB,CAAC;YACDC,QAAQ,EAAE;cACRC,MAAM,EAAEmC,IAAI,CAACC,IAAI,CAACF,KAAK,CAAClC,MAAM,CAACN,CAAC,IAAI,CAAC,GAAGwC,KAAK,CAAClC,MAAM,CAACL,CAAC,IAAI,CAAC,CAAC;cAC5DM,OAAO,EAAEiC,KAAK,CAACjC,OAAO,CAACL;YACzB;UACF,CAAC,CAAC,CAAC;;UAEH;UACA,MAAMyC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;UAC5BlC,WAAW,CAACe,IAAI,KAAK;YACnB1B,CAAC,EAAE,CAAC,GAAG0B,IAAI,CAAC1B,CAAC,CAAC8C,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEP,IAAI,CAACxC,QAAQ,CAACC,CAAC,CAAC;YAAE;YAC5CC,CAAC,EAAE,CAAC,GAAGyB,IAAI,CAACzB,CAAC,CAAC6C,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEP,IAAI,CAACxC,QAAQ,CAACE,CAAC,CAAC;YAC1CW,UAAU,EAAE,CAAC,GAAGc,IAAI,CAACd,UAAU,CAACkC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEH,SAAS;UACvD,CAAC,CAAC,CAAC;QACL;QAEA,IAAIR,OAAO,CAACJ,KAAK,KAAK,KAAK,IAAII,OAAO,CAACG,GAAG,EAAE;UAC1C;UACAxB,aAAa,CAACY,IAAI,IAAI,CAACS,OAAO,CAACG,GAAG,EAAE,GAAGZ,IAAI,CAACoB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D;QAEA,IAAIX,OAAO,CAACJ,KAAK,KAAK,OAAO,IAAII,OAAO,CAACG,GAAG,EAAE;UAC5C;UACAtB,aAAa,CAACU,IAAI,IAAI,CAACS,OAAO,CAACG,GAAG,EAAE,GAAGZ,IAAI,CAACoB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D;QAEA,IAAIX,OAAO,CAACJ,KAAK,KAAK,gBAAgB,IAAII,OAAO,CAACG,GAAG,EAAE;UACrDxC,YAAY,CAAC4B,IAAI,KAAK;YACpB,GAAGA,IAAI;YACPlB,OAAO,EAAE2B,OAAO,CAACG,GAAG,CAACS,UAAU,GAAG;UACpC,CAAC,CAAC,CAAC;QACL;MAEF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdxB,OAAO,CAACwB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;IACF,CAAC;IAED3B,EAAE,CAAC4B,OAAO,GAAG,MAAM;MACjBzB,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/C3B,YAAY,CAAC4B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEjB,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACvD,CAAC;IAEDY,EAAE,CAAC6B,OAAO,GAAIF,KAAK,IAAK;MACtBxB,OAAO,CAACwB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxClD,YAAY,CAAC4B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEjB,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACvD,CAAC;IAED,OAAO,MAAM;MACXY,EAAE,CAAC8B,KAAK,CAAC,CAAC;IACZ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjE,SAAS,CAAC,MAAM;IACd;IACA,MAAMkC,KAAK,GAAG9B,iBAAiB;IAC/B,MAAM+B,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChB;MACAF,EAAE,CAACM,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QACrBC,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;IACL,CAAC;IAEDX,EAAE,CAACY,SAAS,GAAIC,KAAK,IAAK;MACxB,IAAI;QACF,MAAMC,OAAO,GAAGP,IAAI,CAACQ,KAAK,CAACF,KAAK,CAACG,IAAI,CAAC;QAEtC,IAAIF,OAAO,CAACJ,KAAK,KAAK,mBAAmB,IAAII,OAAO,CAACG,GAAG,EAAE;UACxD;UACA,MAAMc,QAAQ,GAAGjB,OAAO,CAACG,GAAG;UAE5B,IAAIc,QAAQ,CAACC,QAAQ,KAAK,MAAM,IAAID,QAAQ,CAACC,QAAQ,KAAK,MAAM,EAAE;YAChE;YACA,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;YAC/CF,MAAM,CAACG,KAAK,GAAGL,QAAQ,CAACK,KAAK;YAC7BH,MAAM,CAACI,MAAM,GAAGN,QAAQ,CAACM,MAAM;YAC/B,MAAMC,GAAG,GAAGL,MAAM,CAACM,UAAU,CAAC,IAAI,CAAC;YAEnC,IAAID,GAAG,EAAE;cACP;cACA,MAAME,SAAS,GAAGF,GAAG,CAACG,eAAe,CAACV,QAAQ,CAACK,KAAK,EAAEL,QAAQ,CAACM,MAAM,CAAC;cACtE,MAAMrB,IAAI,GAAG0B,IAAI,CAACX,QAAQ,CAACf,IAAI,CAAC;cAEhC,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,IAAI,CAAC4B,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;gBACvC,MAAME,UAAU,GAAIF,CAAC,GAAG,CAAC,GAAI,CAAC;gBAC9B,IAAIZ,QAAQ,CAACC,QAAQ,KAAK,MAAM,EAAE;kBAChCQ,SAAS,CAACxB,IAAI,CAAC6B,UAAU,CAAC,GAAG7B,IAAI,CAAC8B,UAAU,CAACH,CAAC,CAAC,CAAC,CAAK;kBACrDH,SAAS,CAACxB,IAAI,CAAC6B,UAAU,GAAG,CAAC,CAAC,GAAG7B,IAAI,CAAC8B,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;kBACzDH,SAAS,CAACxB,IAAI,CAAC6B,UAAU,GAAG,CAAC,CAAC,GAAG7B,IAAI,CAAC8B,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC3D,CAAC,MAAM;kBAAE;kBACPH,SAAS,CAACxB,IAAI,CAAC6B,UAAU,CAAC,GAAG7B,IAAI,CAAC8B,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,CAAK;kBACzDH,SAAS,CAACxB,IAAI,CAAC6B,UAAU,GAAG,CAAC,CAAC,GAAG7B,IAAI,CAAC8B,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;kBACzDH,SAAS,CAACxB,IAAI,CAAC6B,UAAU,GAAG,CAAC,CAAC,GAAG7B,IAAI,CAAC8B,UAAU,CAACH,CAAC,CAAC,CAAC,CAAK;gBAC3D;gBACAH,SAAS,CAACxB,IAAI,CAAC6B,UAAU,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;cACxC;cAEAP,GAAG,CAACS,YAAY,CAACP,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;cACjC3C,cAAc,CAACoC,MAAM,CAACe,SAAS,CAAC,CAAC,CAAC;YACpC;UACF;QACF;MACF,CAAC,CAAC,OAAOrB,KAAK,EAAE;QACdxB,OAAO,CAACwB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;IACF,CAAC;IAED,OAAO,MAAM;MACX3B,EAAE,CAAC8B,KAAK,CAAC,CAAC;IACZ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjE,SAAS,CAAC,MAAM;IACd,MAAMkC,KAAK,GAAG9B,iBAAiB;IAC/B,MAAM+B,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChB;MACAF,EAAE,CAACM,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QACrBC,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;IACL,CAAC;IAEDX,EAAE,CAACY,SAAS,GAAIC,KAAK,IAAK;MACxB,IAAI;QACF,MAAMC,OAAO,GAAGP,IAAI,CAACQ,KAAK,CAACF,KAAK,CAACG,IAAI,CAAC;QAEtC,IAAIF,OAAO,CAACJ,KAAK,KAAK,MAAM,IAAII,OAAO,CAACG,GAAG,IAAInB,YAAY,CAACmD,OAAO,EAAE;UACnE,MAAMC,MAAM,GAAGpC,OAAO,CAACG,GAAG;UAC1B,MAAMgB,MAAM,GAAGnC,YAAY,CAACmD,OAAO;UACnC,MAAMX,GAAG,GAAGL,MAAM,CAACM,UAAU,CAAC,IAAI,CAAC;UAEnC,IAAID,GAAG,EAAE;YACPL,MAAM,CAACG,KAAK,GAAGc,MAAM,CAACC,IAAI,CAACf,KAAK;YAChCH,MAAM,CAACI,MAAM,GAAGa,MAAM,CAACC,IAAI,CAACd,MAAM;YAElC,MAAMG,SAAS,GAAGF,GAAG,CAACG,eAAe,CAACS,MAAM,CAACC,IAAI,CAACf,KAAK,EAAEc,MAAM,CAACC,IAAI,CAACd,MAAM,CAAC;;YAE5E;YACA,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,MAAM,CAAClC,IAAI,CAAC4B,MAAM,EAAED,CAAC,EAAE,EAAE;cAC3C,MAAMS,KAAK,GAAGF,MAAM,CAAClC,IAAI,CAAC2B,CAAC,CAAC;cAC5B,MAAME,UAAU,GAAGF,CAAC,GAAG,CAAC;cAExB,IAAIS,KAAK,KAAK,CAAC,CAAC,EAAE;gBAChB;gBACAZ,SAAS,CAACxB,IAAI,CAAC6B,UAAU,CAAC,GAAG,GAAG,CAAC,CAAK;gBACtCL,SAAS,CAACxB,IAAI,CAAC6B,UAAU,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;gBACtCL,SAAS,CAACxB,IAAI,CAAC6B,UAAU,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;cACxC,CAAC,MAAM,IAAIO,KAAK,KAAK,CAAC,EAAE;gBACtB;gBACAZ,SAAS,CAACxB,IAAI,CAAC6B,UAAU,CAAC,GAAG,GAAG,CAAC,CAAK;gBACtCL,SAAS,CAACxB,IAAI,CAAC6B,UAAU,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;gBACtCL,SAAS,CAACxB,IAAI,CAAC6B,UAAU,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;cACxC,CAAC,MAAM;gBACL;gBACAL,SAAS,CAACxB,IAAI,CAAC6B,UAAU,CAAC,GAAG,CAAC,CAAC,CAAO;gBACtCL,SAAS,CAACxB,IAAI,CAAC6B,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAG;gBACtCL,SAAS,CAACxB,IAAI,CAAC6B,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAG;cACxC;cACAL,SAAS,CAACxB,IAAI,CAAC6B,UAAU,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;YACxC;YAEAP,GAAG,CAACS,YAAY,CAACP,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;UACnC;QACF;MACF,CAAC,CAAC,OAAOb,KAAK,EAAE;QACdxB,OAAO,CAACwB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;IACF,CAAC;IAED,OAAO,MAAM;MACX3B,EAAE,CAAC8B,KAAK,CAAC,CAAC;IACZ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjE,SAAS,CAAC,MAAM;IACd,IAAIiC,YAAY,CAACmD,OAAO,IAAIzE,SAAS,CAACY,SAAS,EAAE;MAC/C,MAAM6C,MAAM,GAAGnC,YAAY,CAACmD,OAAO;MACnC,MAAMX,GAAG,GAAGL,MAAM,CAACM,UAAU,CAAC,IAAI,CAAC;MAEnC,IAAID,GAAG,EAAE;QACP;QACA;QACA,MAAMe,MAAM,GAAI7E,SAAS,CAACE,QAAQ,CAACC,CAAC,GAAG,EAAE,GAAIsD,MAAM,CAACG,KAAK,GAAG,CAAC,CAAC,CAAE;QAChE,MAAMkB,MAAM,GAAGrB,MAAM,CAACI,MAAM,IAAK7D,SAAS,CAACE,QAAQ,CAACE,CAAC,GAAG,EAAE,GAAIqD,MAAM,CAACI,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;QAElF;QACAC,GAAG,CAACiB,SAAS,GAAG,SAAS;QACzBjB,GAAG,CAACkB,SAAS,CAAC,CAAC;QACflB,GAAG,CAACmB,GAAG,CAACJ,MAAM,EAAEC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGlC,IAAI,CAACsC,EAAE,CAAC;QAC1CpB,GAAG,CAACqB,IAAI,CAAC,CAAC;;QAEV;QACA,MAAMC,KAAK,GAAGxC,IAAI,CAACyC,KAAK,CACtB,CAAC,IAAIrF,SAAS,CAACM,WAAW,CAACC,CAAC,GAAGP,SAAS,CAACM,WAAW,CAACD,CAAC,GAAGL,SAAS,CAACM,WAAW,CAACH,CAAC,GAAGH,SAAS,CAACM,WAAW,CAACF,CAAC,CAAC,EAC3G,CAAC,GAAG,CAAC,IAAIJ,SAAS,CAACM,WAAW,CAACF,CAAC,GAAGJ,SAAS,CAACM,WAAW,CAACF,CAAC,GAAGJ,SAAS,CAACM,WAAW,CAACD,CAAC,GAAGL,SAAS,CAACM,WAAW,CAACD,CAAC,CAChH,CAAC;QAEDyD,GAAG,CAACwB,WAAW,GAAG,MAAM;QACxBxB,GAAG,CAACyB,SAAS,GAAG,CAAC;QACjBzB,GAAG,CAACkB,SAAS,CAAC,CAAC;QACflB,GAAG,CAAC0B,MAAM,CAACX,MAAM,EAAEC,MAAM,CAAC;QAC1BhB,GAAG,CAAC2B,MAAM,CACRZ,MAAM,GAAGjC,IAAI,CAAC8C,GAAG,CAACN,KAAK,CAAC,GAAG,EAAE,EAC7BN,MAAM,GAAGlC,IAAI,CAAC+C,GAAG,CAACP,KAAK,CAAC,GAAG,EAAE,CAAE;QACjC,CAAC;QACDtB,GAAG,CAAC8B,MAAM,CAAC,CAAC;MACd;IACF;EACF,CAAC,EAAE,CAAC5F,SAAS,CAACE,QAAQ,EAAEF,SAAS,CAACM,WAAW,EAAEN,SAAS,CAACY,SAAS,CAAC,CAAC;EAEpE,MAAMiF,eAAe,GAAIC,KAAa,IAAa;IACjD,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,SAAS;IAChC,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,SAAS;IAChC,OAAO,SAAS;EAClB,CAAC;EAED,oBACEtG,OAAA;IAAKuG,KAAK,EAAE;MACVC,UAAU,EAAE,SAAS;MACrBC,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,WAAW;MACvBC,OAAO,EAAE;IACX,CAAE;IAAAC,QAAA,gBAEA7G,OAAA;MAAKuG,KAAK,EAAE;QACVO,SAAS,EAAE,QAAQ;QACnBC,YAAY,EAAE,MAAM;QACpBC,YAAY,EAAE,gBAAgB;QAC9BC,aAAa,EAAE;MACjB,CAAE;MAAAJ,QAAA,gBACA7G,OAAA;QAAIuG,KAAK,EAAE;UAAEG,KAAK,EAAE,SAAS;UAAEQ,MAAM,EAAE;QAAa,CAAE;QAAAL,QAAA,EAAC;MAEvD;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLtH,OAAA;QAAKuG,KAAK,EAAE;UACVgB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,GAAG,EAAE,MAAM;UACXC,QAAQ,EAAE;QACZ,CAAE;QAAAd,QAAA,gBACA7G,OAAA;UAAKuG,KAAK,EAAE;YACVnC,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACduD,YAAY,EAAE,KAAK;YACnBC,eAAe,EAAErH,SAAS,CAACY,SAAS,GAAG,SAAS,GAAG;UACrD;QAAE;UAAA+F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACTtH,OAAA;UAAA6G,QAAA,EAAOrG,SAAS,CAACY,SAAS,GAAG,mBAAmB,GAAG;QAAc;UAAA+F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzEtH,OAAA;UAAMuG,KAAK,EAAE;YAAEuB,UAAU,EAAE,MAAM;YAAEH,QAAQ,EAAE,MAAM;YAAEjB,KAAK,EAAE;UAAO,CAAE;UAAAG,QAAA,EAAC;QAEtE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtH,OAAA;MAAKuG,KAAK,EAAE;QACVgB,OAAO,EAAE,MAAM;QACfQ,mBAAmB,EAAE,sCAAsC;QAC3DL,GAAG,EAAE,MAAM;QACXX,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBAGA7G,OAAA;QAAKuG,KAAK,EAAE;UACVsB,eAAe,EAAE,SAAS;UAC1BG,MAAM,EAAE,gBAAgB;UACxBJ,YAAY,EAAE,KAAK;UACnBhB,OAAO,EAAE;QACX,CAAE;QAAAC,QAAA,gBACA7G,OAAA;UAAIuG,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEuB,SAAS,EAAE;UAAE,CAAE;UAAApB,QAAA,EAAC;QAA+C;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnGtH,OAAA;UAAKuG,KAAK,EAAE;YACVlC,MAAM,EAAE,OAAO;YACfwD,eAAe,EAAE,MAAM;YACvBD,YAAY,EAAE,KAAK;YACnBI,MAAM,EAAE,gBAAgB;YACxBtH,QAAQ,EAAE,UAAU;YACpBwH,QAAQ,EAAE;UACZ,CAAE;UAAArB,QAAA,eACA7G,OAAA;YACEmI,GAAG,EAAGlE,MAAM,IAAK;cACf,IAAIA,MAAM,IAAI5C,QAAQ,CAACV,CAAC,CAACiE,MAAM,GAAG,CAAC,EAAE;gBACnC,MAAMN,GAAG,GAAGL,MAAM,CAACM,UAAU,CAAC,IAAI,CAAC;gBACnC,IAAID,GAAG,EAAE;kBACPL,MAAM,CAACG,KAAK,GAAGH,MAAM,CAACmE,WAAW;kBACjCnE,MAAM,CAACI,MAAM,GAAGJ,MAAM,CAACoE,YAAY;kBAEnC/D,GAAG,CAACgE,SAAS,CAAC,CAAC,EAAE,CAAC,EAAErE,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACI,MAAM,CAAC;;kBAEhD;kBACAC,GAAG,CAACwB,WAAW,GAAG,MAAM;kBACxBxB,GAAG,CAACyB,SAAS,GAAG,CAAC;kBACjB,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,MAAM,CAACG,KAAK,EAAEO,CAAC,IAAI,EAAE,EAAE;oBACzCL,GAAG,CAACkB,SAAS,CAAC,CAAC;oBACflB,GAAG,CAAC0B,MAAM,CAACrB,CAAC,EAAE,CAAC,CAAC;oBAChBL,GAAG,CAAC2B,MAAM,CAACtB,CAAC,EAAEV,MAAM,CAACI,MAAM,CAAC;oBAC5BC,GAAG,CAAC8B,MAAM,CAAC,CAAC;kBACd;kBACA,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,MAAM,CAACI,MAAM,EAAEM,CAAC,IAAI,EAAE,EAAE;oBAC1CL,GAAG,CAACkB,SAAS,CAAC,CAAC;oBACflB,GAAG,CAAC0B,MAAM,CAAC,CAAC,EAAErB,CAAC,CAAC;oBAChBL,GAAG,CAAC2B,MAAM,CAAChC,MAAM,CAACG,KAAK,EAAEO,CAAC,CAAC;oBAC3BL,GAAG,CAAC8B,MAAM,CAAC,CAAC;kBACd;;kBAEA;kBACA,IAAI/E,QAAQ,CAACV,CAAC,CAACiE,MAAM,GAAG,CAAC,EAAE;oBACzBN,GAAG,CAACwB,WAAW,GAAG,SAAS;oBAC3BxB,GAAG,CAACyB,SAAS,GAAG,CAAC;oBACjBzB,GAAG,CAACkB,SAAS,CAAC,CAAC;oBAEf,MAAM+C,IAAI,GAAGnF,IAAI,CAACoF,GAAG,CAAC,GAAGnH,QAAQ,CAACV,CAAC,CAAC;oBACpC,MAAM8H,IAAI,GAAGrF,IAAI,CAACsF,GAAG,CAAC,GAAGrH,QAAQ,CAACV,CAAC,CAAC;oBACpC,MAAMgI,KAAK,GAAGF,IAAI,GAAGF,IAAI,IAAI,CAAC;oBAE9BlH,QAAQ,CAACV,CAAC,CAACiI,OAAO,CAAC,CAACjI,CAAC,EAAEgE,CAAC,KAAK;sBAC3B,MAAMkE,OAAO,GAAIlE,CAAC,IAAItD,QAAQ,CAACV,CAAC,CAACiE,MAAM,GAAG,CAAC,CAAC,GAAIX,MAAM,CAACG,KAAK;sBAC5D,MAAM0E,OAAO,GAAG7E,MAAM,CAACI,MAAM,GAAI,CAAC1D,CAAC,GAAG4H,IAAI,IAAII,KAAK,GAAI1E,MAAM,CAACI,MAAM;sBAEpE,IAAIM,CAAC,KAAK,CAAC,EAAE;wBACXL,GAAG,CAAC0B,MAAM,CAAC6C,OAAO,EAAEC,OAAO,CAAC;sBAC9B,CAAC,MAAM;wBACLxE,GAAG,CAAC2B,MAAM,CAAC4C,OAAO,EAAEC,OAAO,CAAC;sBAC9B;oBACF,CAAC,CAAC;oBAEFxE,GAAG,CAAC8B,MAAM,CAAC,CAAC;kBACd;gBACF;cACF;YACF,CAAE;YACFG,KAAK,EAAE;cAAEnC,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAO;UAAE;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtH,OAAA;UAAKuG,KAAK,EAAE;YAAEoB,QAAQ,EAAE,MAAM;YAAEjB,KAAK,EAAE,MAAM;YAAEuB,SAAS,EAAE;UAAO,CAAE;UAAApB,QAAA,GAAC,aACvD,EAACrG,SAAS,CAACE,QAAQ,CAACC,CAAC,CAACoI,OAAO,CAAC,CAAC,CAAC,EAAC,cAAY,EAAC1H,QAAQ,CAACV,CAAC,CAACiE,MAAM;QAAA;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtH,OAAA;QAAKuG,KAAK,EAAE;UACVsB,eAAe,EAAE,SAAS;UAC1BG,MAAM,EAAE,gBAAgB;UACxBJ,YAAY,EAAE,KAAK;UACnBhB,OAAO,EAAE;QACX,CAAE;QAAAC,QAAA,gBACA7G,OAAA;UAAIuG,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEuB,SAAS,EAAE;UAAE,CAAE;UAAApB,QAAA,EAAC;QAAqB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzEtH,OAAA;UAAKuG,KAAK,EAAE;YACVlC,MAAM,EAAE,OAAO;YACfwD,eAAe,EAAE,MAAM;YACvBD,YAAY,EAAE,KAAK;YACnBI,MAAM,EAAE,gBAAgB;YACxBpB,OAAO,EAAE,MAAM;YACfsB,QAAQ,EAAE,MAAM;YAChBvB,UAAU,EAAE,WAAW;YACvBgB,QAAQ,EAAE;UACZ,CAAE;UAAAd,QAAA,EACCrF,UAAU,CAACoD,MAAM,GAAG,CAAC,GACpBpD,UAAU,CAACwH,GAAG,CAAC,CAAC/F,GAAG,EAAEgG,KAAK;YAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;YAAA,oBACxB3J,OAAA;cAAiBuG,KAAK,EAAE;gBACtBQ,YAAY,EAAE,KAAK;gBACnBL,KAAK,EAAEuC,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,MAAM;gBACvCjC,YAAY,EAAE,gBAAgB;gBAC9BC,aAAa,EAAE;cACjB,CAAE;cAAAJ,QAAA,gBACA7G,OAAA;gBAAKuG,KAAK,EAAE;kBAAEG,KAAK,EAAE;gBAAU,CAAE;gBAAAG,QAAA,GAAC,aACrB,EAACoC,KAAK,GAAG,CAAC,EAAC,GACxB;cAAA;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtH,OAAA;gBAAA6G,QAAA,GAAK,UACK,EAAC,EAAAqC,eAAA,GAAAjG,GAAG,CAAC2G,UAAU,cAAAV,eAAA,wBAAAC,gBAAA,GAAdD,eAAA,CAAiB,CAAC,CAAC,cAAAC,gBAAA,wBAAAC,qBAAA,GAAnBD,gBAAA,CAAqBU,MAAM,cAAAT,qBAAA,uBAA3BA,qBAAA,CAA6BU,QAAQ,KAAI,KAAK,EAAC,UAAG,EAAC,EAAAT,gBAAA,GAAApG,GAAG,CAAC2G,UAAU,cAAAP,gBAAA,wBAAAC,iBAAA,GAAdD,gBAAA,CAAiB,CAAC,CAAC,cAAAC,iBAAA,uBAAnBA,iBAAA,CAAqBS,cAAc,KAAI,KAAK;cAAA;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrG,CAAC,EACL,EAAAiC,gBAAA,GAAAtG,GAAG,CAAC2G,UAAU,cAAAL,gBAAA,wBAAAC,iBAAA,GAAdD,gBAAA,CAAiB,CAAC,CAAC,cAAAC,iBAAA,uBAAnBA,iBAAA,CAAqBQ,SAAS,kBAC7BhK,OAAA;gBAAA6G,QAAA,GAAK,gBACW,GAAA4C,qBAAA,GAACxG,GAAG,CAAC2G,UAAU,CAAC,CAAC,CAAC,CAACI,SAAS,CAACC,WAAW,CAACtJ,CAAC,cAAA8I,qBAAA,uBAAzCA,qBAAA,CAA2CV,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,GAAAW,sBAAA,GAACzG,GAAG,CAAC2G,UAAU,CAAC,CAAC,CAAC,CAACI,SAAS,CAACC,WAAW,CAACrJ,CAAC,cAAA8I,sBAAA,uBAAzCA,sBAAA,CAA2CX,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,GAAAY,sBAAA,GAAC1G,GAAG,CAAC2G,UAAU,CAAC,CAAC,CAAC,CAACI,SAAS,CAACC,WAAW,CAACpJ,CAAC,cAAA8I,sBAAA,uBAAzCA,sBAAA,CAA2CZ,OAAO,CAAC,CAAC,CAAC,EAAC,GACzL;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA,GAhBO2B,KAAK;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBV,CAAC;UAAA,CACP,CAAC,gBAEFtH,OAAA;YAAKuG,KAAK,EAAE;cAAEG,KAAK,EAAE,MAAM;cAAEI,SAAS,EAAE,QAAQ;cAAEmB,SAAS,EAAE;YAAO,CAAE;YAAApB,QAAA,EAAC;UAEvE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNtH,OAAA;UAAKuG,KAAK,EAAE;YAAEoB,QAAQ,EAAE,MAAM;YAAEjB,KAAK,EAAE,MAAM;YAAEuB,SAAS,EAAE;UAAO,CAAE;UAAApB,QAAA,GAAC,qBAC/C,EAACrF,UAAU,CAACoD,MAAM,EAAC,eACxC;QAAA;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtH,OAAA;QAAKuG,KAAK,EAAE;UACVsB,eAAe,EAAE,SAAS;UAC1BG,MAAM,EAAE,gBAAgB;UACxBJ,YAAY,EAAE,KAAK;UACnBhB,OAAO,EAAE;QACX,CAAE;QAAAC,QAAA,gBACA7G,OAAA;UAAIuG,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEuB,SAAS,EAAE;UAAE,CAAE;UAAApB,QAAA,EAAC;QAAe;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEnEtH,OAAA;UAAKuG,KAAK,EAAE;YAAEQ,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBACnC7G,OAAA;YAAKuG,KAAK,EAAE;cAAEgB,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE,eAAe;cAAEV,YAAY,EAAE;YAAM,CAAE;YAAAF,QAAA,eACpF7G,OAAA;cAAA6G,QAAA,EAAM;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACNtH,OAAA;YAAKuG,KAAK,EAAE;cAAEoB,QAAQ,EAAE,MAAM;cAAEjB,KAAK,EAAE;YAAO,CAAE;YAAAG,QAAA,GAAC,KAC5C,EAACrG,SAAS,CAACE,QAAQ,CAACC,CAAC,CAACoI,OAAO,CAAC,CAAC,CAAC,EAAC,SACjC,EAACvI,SAAS,CAACE,QAAQ,CAACE,CAAC,CAACmI,OAAO,CAAC,CAAC,CAAC,EAAC,SACjC,EAACvI,SAAS,CAACE,QAAQ,CAACG,CAAC,CAACkI,OAAO,CAAC,CAAC,CAAC,EAAC,GACtC;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtH,OAAA;UAAKuG,KAAK,EAAE;YAAEQ,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBACnC7G,OAAA;YAAKuG,KAAK,EAAE;cAAEgB,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE,eAAe;cAAEV,YAAY,EAAE;YAAM,CAAE;YAAAF,QAAA,eACpF7G,OAAA;cAAA6G,QAAA,EAAM;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACNtH,OAAA;YAAKuG,KAAK,EAAE;cAAEoB,QAAQ,EAAE,MAAM;cAAEjB,KAAK,EAAE;YAAO,CAAE;YAAAG,QAAA,GAAC,UACvC,EAACrG,SAAS,CAACQ,QAAQ,CAACC,MAAM,CAAC8H,OAAO,CAAC,CAAC,CAAC,EAAC,iBACrC,EAACvI,SAAS,CAACQ,QAAQ,CAACE,OAAO,CAAC6H,OAAO,CAAC,CAAC,CAAC,EAAC,OAClD;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtH,OAAA;UAAKuG,KAAK,EAAE;YAAEQ,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBACnC7G,OAAA;YAAKuG,KAAK,EAAE;cAAEgB,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE,eAAe;cAAEV,YAAY,EAAE;YAAM,CAAE;YAAAF,QAAA,gBACpF7G,OAAA;cAAA6G,QAAA,EAAM;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrBtH,OAAA;cAAMuG,KAAK,EAAE;gBAAEG,KAAK,EAAEL,eAAe,CAAC7F,SAAS,CAACW,OAAO;cAAE,CAAE;cAAA0F,QAAA,GACxDrG,SAAS,CAACW,OAAO,CAAC4H,OAAO,CAAC,CAAC,CAAC,EAAC,GAChC;YAAA;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNtH,OAAA;YAAKuG,KAAK,EAAE;cACVnC,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,KAAK;cACbwD,eAAe,EAAE,MAAM;cACvBD,YAAY,EAAE,KAAK;cACnBM,QAAQ,EAAE;YACZ,CAAE;YAAArB,QAAA,eACA7G,OAAA;cAAKuG,KAAK,EAAE;gBACVnC,KAAK,EAAE,GAAG5D,SAAS,CAACW,OAAO,GAAG;gBAC9BkD,MAAM,EAAE,MAAM;gBACdwD,eAAe,EAAExB,eAAe,CAAC7F,SAAS,CAACW,OAAO,CAAC;gBACnD+I,UAAU,EAAE;cACd;YAAE;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtH,OAAA;QAAKuG,KAAK,EAAE;UACVsB,eAAe,EAAE,SAAS;UAC1BG,MAAM,EAAE,gBAAgB;UACxBJ,YAAY,EAAE,KAAK;UACnBhB,OAAO,EAAE;QACX,CAAE;QAAAC,QAAA,gBACA7G,OAAA;UAAIuG,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEuB,SAAS,EAAE;UAAE,CAAE;UAAApB,QAAA,EAAC;QAAc;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClEtH,OAAA;UAAKuG,KAAK,EAAE;YACVgB,OAAO,EAAE,MAAM;YACfE,cAAc,EAAE,QAAQ;YACxBD,UAAU,EAAE,QAAQ;YACpBf,SAAS,EAAE,OAAO;YAClBoB,eAAe,EAAE,MAAM;YACvBD,YAAY,EAAE,KAAK;YACnBI,MAAM,EAAE;UACV,CAAE;UAAAnB,QAAA,EACCjF,WAAW,gBACV5B,OAAA;YACEmK,GAAG,EAAEvI,WAAY;YACjBwI,GAAG,EAAC,cAAc;YAClB7D,KAAK,EAAE;cACL8D,QAAQ,EAAE,MAAM;cAChBC,SAAS,EAAE,OAAO;cAClB1C,YAAY,EAAE;YAChB;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEFtH,OAAA;YAAKuG,KAAK,EAAE;cAAEG,KAAK,EAAE,MAAM;cAAEI,SAAS,EAAE;YAAS,CAAE;YAAAD,QAAA,gBACjD7G,OAAA;cAAKuG,KAAK,EAAE;gBAAEoB,QAAQ,EAAE,MAAM;gBAAEZ,YAAY,EAAE;cAAO,CAAE;cAAAF,QAAA,EAAC;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChEtH,OAAA;cAAA6G,QAAA,EAAK;YAAc;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNtH,OAAA;UAAKuG,KAAK,EAAE;YACVoB,QAAQ,EAAE,MAAM;YAChBjB,KAAK,EAAE,MAAM;YACbuB,SAAS,EAAE,MAAM;YACjBnB,SAAS,EAAE;UACb,CAAE;UAAAD,QAAA,EAAC;QAEH;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtH,OAAA;QAAKuG,KAAK,EAAE;UACVsB,eAAe,EAAE,SAAS;UAC1BG,MAAM,EAAE,gBAAgB;UACxBJ,YAAY,EAAE,KAAK;UACnBhB,OAAO,EAAE;QACX,CAAE;QAAAC,QAAA,gBACA7G,OAAA;UAAIuG,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEuB,SAAS,EAAE;UAAE,CAAE;UAAApB,QAAA,EAAC;QAAyB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7EtH,OAAA;UAAKuG,KAAK,EAAE;YACVlC,MAAM,EAAE,OAAO;YACfwD,eAAe,EAAE,MAAM;YACvBD,YAAY,EAAE,KAAK;YACnBI,MAAM,EAAE,gBAAgB;YACxBtH,QAAQ,EAAE,UAAU;YACpBwH,QAAQ,EAAE;UACZ,CAAE;UAAArB,QAAA,eACA7G,OAAA;YACEmI,GAAG,EAAGlE,MAAM,IAAK;cACf,IAAIA,MAAM,IAAIvC,UAAU,CAACkD,MAAM,GAAG,CAAC,EAAE;gBACnC,MAAMN,GAAG,GAAGL,MAAM,CAACM,UAAU,CAAC,IAAI,CAAC;gBACnC,IAAID,GAAG,EAAE;kBACPL,MAAM,CAACG,KAAK,GAAGH,MAAM,CAACmE,WAAW;kBACjCnE,MAAM,CAACI,MAAM,GAAGJ,MAAM,CAACoE,YAAY;kBAEnC/D,GAAG,CAACgE,SAAS,CAAC,CAAC,EAAE,CAAC,EAAErE,MAAM,CAACG,KAAK,EAAEH,MAAM,CAACI,MAAM,CAAC;;kBAEhD;kBACA,MAAMkG,OAAO,GAAGtG,MAAM,CAACG,KAAK,GAAG,CAAC;kBAChC,MAAMoG,OAAO,GAAGvG,MAAM,CAACI,MAAM,GAAG,CAAC;;kBAEjC;kBACAC,GAAG,CAACwB,WAAW,GAAG,MAAM;kBACxBxB,GAAG,CAACyB,SAAS,GAAG,CAAC;kBACjBzB,GAAG,CAACkB,SAAS,CAAC,CAAC;kBACflB,GAAG,CAAC0B,MAAM,CAAC,CAAC,EAAEwE,OAAO,CAAC;kBACtBlG,GAAG,CAAC2B,MAAM,CAAChC,MAAM,CAACG,KAAK,EAAEoG,OAAO,CAAC;kBACjClG,GAAG,CAAC0B,MAAM,CAACuE,OAAO,EAAE,CAAC,CAAC;kBACtBjG,GAAG,CAAC2B,MAAM,CAACsE,OAAO,EAAEtG,MAAM,CAACI,MAAM,CAAC;kBAClCC,GAAG,CAAC8B,MAAM,CAAC,CAAC;;kBAEZ;kBACA,MAAMqE,IAAI,GAAG/I,UAAU,CAAC,CAAC,CAAC;kBAC1B,IAAI+I,IAAI,IAAIA,IAAI,CAACC,MAAM,EAAE;oBACvBpG,GAAG,CAACiB,SAAS,GAAG,SAAS;oBACzB,MAAMoF,QAAQ,GAAGF,IAAI,CAACG,SAAS,IAAI,CAACxH,IAAI,CAACsC,EAAE;oBAC3C,MAAMmF,cAAc,GAAGJ,IAAI,CAACK,eAAe,IAAK,CAAC,GAAG1H,IAAI,CAACsC,EAAE,GAAG+E,IAAI,CAACC,MAAM,CAAC9F,MAAO;oBACjF,MAAMmG,KAAK,GAAG,EAAE,CAAC,CAAC;;oBAElBN,IAAI,CAACC,MAAM,CAAC9B,OAAO,CAAC,CAACD,KAAa,EAAEhE,CAAS,KAAK;sBAChD,IAAIgE,KAAK,GAAG8B,IAAI,CAACO,SAAS,IAAIrC,KAAK,GAAG8B,IAAI,CAACQ,SAAS,EAAE;wBACpD,MAAMrF,KAAK,GAAG+E,QAAQ,GAAGhG,CAAC,GAAGkG,cAAc;wBAC3C,MAAMlK,CAAC,GAAG4J,OAAO,GAAG5B,KAAK,GAAGvF,IAAI,CAAC8C,GAAG,CAACN,KAAK,CAAC,GAAGmF,KAAK;wBACnD,MAAMnK,CAAC,GAAG4J,OAAO,GAAG7B,KAAK,GAAGvF,IAAI,CAAC+C,GAAG,CAACP,KAAK,CAAC,GAAGmF,KAAK;wBAEnD,IAAIpK,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGsD,MAAM,CAACG,KAAK,IAAIxD,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGqD,MAAM,CAACI,MAAM,EAAE;0BAC7DC,GAAG,CAACkB,SAAS,CAAC,CAAC;0BACflB,GAAG,CAACmB,GAAG,CAAC9E,CAAC,EAAEC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGwC,IAAI,CAACsC,EAAE,CAAC;0BAChCpB,GAAG,CAACqB,IAAI,CAAC,CAAC;wBACZ;sBACF;oBACF,CAAC,CAAC;;oBAEF;oBACArB,GAAG,CAACiB,SAAS,GAAG,SAAS;oBACzBjB,GAAG,CAACkB,SAAS,CAAC,CAAC;oBACflB,GAAG,CAACmB,GAAG,CAAC8E,OAAO,EAAEC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGpH,IAAI,CAACsC,EAAE,CAAC;oBAC5CpB,GAAG,CAACqB,IAAI,CAAC,CAAC;kBACZ;gBACF;cACF;YACF,CAAE;YACFY,KAAK,EAAE;cAAEnC,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAO;UAAE;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtH,OAAA;UAAKuG,KAAK,EAAE;YAAEoB,QAAQ,EAAE,MAAM;YAAEjB,KAAK,EAAE,MAAM;YAAEuB,SAAS,EAAE;UAAO,CAAE;UAAApB,QAAA,GAAC,qBAC/C,EAAC,EAAAzG,YAAA,GAAAsB,UAAU,CAAC,CAAC,CAAC,cAAAtB,YAAA,wBAAAC,mBAAA,GAAbD,YAAA,CAAesK,MAAM,cAAArK,mBAAA,uBAArBA,mBAAA,CAAuBuE,MAAM,KAAI,CAAC,EAAC,YAAU,EAAC,EAAAtE,aAAA,GAAAoB,UAAU,CAAC,CAAC,CAAC,cAAApB,aAAA,wBAAAC,qBAAA,GAAbD,aAAA,CAAe2K,SAAS,cAAA1K,qBAAA,uBAAxBA,qBAAA,CAA0BwI,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,EAAC,GAClH;QAAA;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtH,OAAA;QAAKuG,KAAK,EAAE;UACVsB,eAAe,EAAE,SAAS;UAC1BG,MAAM,EAAE,gBAAgB;UACxBJ,YAAY,EAAE,KAAK;UACnBhB,OAAO,EAAE;QACX,CAAE;QAAAC,QAAA,gBACA7G,OAAA;UAAIuG,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEuB,SAAS,EAAE;UAAE,CAAE;UAAApB,QAAA,EAAC;QAAoB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEtH,OAAA;UAAKuG,KAAK,EAAE;YACVgB,OAAO,EAAE,MAAM;YACfE,cAAc,EAAE,QAAQ;YACxBD,UAAU,EAAE,QAAQ;YACpBK,eAAe,EAAE,MAAM;YACvBD,YAAY,EAAE,KAAK;YACnBI,MAAM,EAAE;UACV,CAAE;UAAAnB,QAAA,eACA7G,OAAA;YACEmI,GAAG,EAAErG,YAAa;YAClByE,KAAK,EAAE;cACL8D,QAAQ,EAAE,MAAM;cAChBzC,YAAY,EAAE;YAChB;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtH,OAAA;UAAKuG,KAAK,EAAE;YACVoB,QAAQ,EAAE,MAAM;YAChBjB,KAAK,EAAE,MAAM;YACbuB,SAAS,EAAE,MAAM;YACjBnB,SAAS,EAAE;UACb,CAAE;UAAAD,QAAA,EAAC;QAEH;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtH,OAAA;MAAKuG,KAAK,EAAE;QACVO,SAAS,EAAE,QAAQ;QACnBF,OAAO,EAAE,MAAM;QACfsE,SAAS,EAAE,gBAAgB;QAC3BxE,KAAK,EAAE,MAAM;QACbiB,QAAQ,EAAE;MACZ,CAAE;MAAAd,QAAA,gBACA7G,OAAA;QAAA6G,QAAA,EAAK;MAAkD;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC7DtH,OAAA;QAAKuG,KAAK,EAAE;UAAE0B,SAAS,EAAE;QAAM,CAAE;QAAApB,QAAA,GAAC,6FAC2D,EAAC5G,iBAAiB;MAAA;QAAAkH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnH,EAAA,CAxsBID,cAAwB;AAAAiL,EAAA,GAAxBjL,cAAwB;AA0sB9B,eAAeA,cAAc;AAAC,IAAAiL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}