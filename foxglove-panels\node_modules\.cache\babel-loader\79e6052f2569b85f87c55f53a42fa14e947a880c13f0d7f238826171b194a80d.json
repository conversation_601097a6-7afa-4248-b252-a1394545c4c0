{"ast": null, "code": "var _jsxFileName = \"F:\\\\foxglove\\\\foxglove-panels\\\\src\\\\components\\\\RobotDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RobotDashboard = () => {\n  _s();\n  const [robotData, setRobotData] = useState({\n    position: {\n      x: 0,\n      y: 0,\n      z: 0\n    },\n    orientation: {\n      x: 0,\n      y: 0,\n      z: 0,\n      w: 1\n    },\n    velocity: {\n      linear: 0,\n      angular: 0\n    },\n    battery: 85,\n    connected: false\n  });\n  const [cameraImage, setCameraImage] = useState(null);\n  const [mapData, setMapData] = useState(null);\n  const canvasRef = useRef(null);\n  const mapCanvasRef = useRef(null);\n\n  // Simulate robot data updates\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setRobotData(prev => ({\n        ...prev,\n        position: {\n          x: prev.position.x + (Math.random() - 0.5) * 0.1,\n          y: prev.position.y + (Math.random() - 0.5) * 0.1,\n          z: 0\n        },\n        velocity: {\n          linear: Math.random() * 2,\n          angular: (Math.random() - 0.5) * 1\n        },\n        battery: Math.max(0, prev.battery - Math.random() * 0.1),\n        connected: Math.random() > 0.1 // 90% uptime\n      }));\n    }, 1000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Simulate camera feed\n  useEffect(() => {\n    const interval = setInterval(() => {\n      // Create a simple simulated camera image\n      const canvas = document.createElement('canvas');\n      canvas.width = 320;\n      canvas.height = 240;\n      const ctx = canvas.getContext('2d');\n      if (ctx) {\n        // Create gradient background\n        const gradient = ctx.createLinearGradient(0, 0, 320, 240);\n        gradient.addColorStop(0, '#1a1a2e');\n        gradient.addColorStop(1, '#16213e');\n        ctx.fillStyle = gradient;\n        ctx.fillRect(0, 0, 320, 240);\n\n        // Add some \"objects\" to simulate camera view\n        ctx.fillStyle = '#ff6b6b';\n        ctx.fillRect(50 + Math.random() * 100, 50 + Math.random() * 100, 30, 30);\n        ctx.fillStyle = '#4ecdc4';\n        ctx.beginPath();\n        ctx.arc(200 + Math.random() * 50, 120 + Math.random() * 50, 20, 0, 2 * Math.PI);\n        ctx.fill();\n\n        // Add timestamp\n        ctx.fillStyle = '#fff';\n        ctx.font = '12px monospace';\n        ctx.fillText(new Date().toLocaleTimeString(), 10, 20);\n        setCameraImage(canvas.toDataURL());\n      }\n    }, 100); // 10 FPS\n\n    return () => clearInterval(interval);\n  }, []);\n\n  // Draw simple map\n  useEffect(() => {\n    if (mapCanvasRef.current) {\n      const canvas = mapCanvasRef.current;\n      const ctx = canvas.getContext('2d');\n      if (ctx) {\n        canvas.width = 400;\n        canvas.height = 300;\n\n        // Clear canvas\n        ctx.fillStyle = '#000';\n        ctx.fillRect(0, 0, 400, 300);\n\n        // Draw grid\n        ctx.strokeStyle = '#333';\n        ctx.lineWidth = 1;\n        for (let i = 0; i < 400; i += 20) {\n          ctx.beginPath();\n          ctx.moveTo(i, 0);\n          ctx.lineTo(i, 300);\n          ctx.stroke();\n        }\n        for (let i = 0; i < 300; i += 20) {\n          ctx.beginPath();\n          ctx.moveTo(0, i);\n          ctx.lineTo(400, i);\n          ctx.stroke();\n        }\n\n        // Draw obstacles\n        ctx.fillStyle = '#666';\n        ctx.fillRect(100, 100, 50, 50);\n        ctx.fillRect(250, 150, 30, 80);\n        ctx.fillRect(50, 200, 80, 20);\n\n        // Draw robot position\n        const robotX = 200 + robotData.position.x * 50;\n        const robotY = 150 + robotData.position.y * 50;\n        ctx.fillStyle = '#ff4444';\n        ctx.beginPath();\n        ctx.arc(robotX, robotY, 8, 0, 2 * Math.PI);\n        ctx.fill();\n\n        // Draw robot orientation\n        ctx.strokeStyle = '#fff';\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.moveTo(robotX, robotY);\n        ctx.lineTo(robotX + Math.cos(0) * 15, robotY + Math.sin(0) * 15);\n        ctx.stroke();\n      }\n    }\n  }, [robotData.position]);\n  const getBatteryColor = level => {\n    if (level > 50) return '#4CAF50';\n    if (level > 20) return '#FF9800';\n    return '#F44336';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: '#0d1117',\n      minHeight: '100vh',\n      color: 'white',\n      fontFamily: 'monospace',\n      padding: '20px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        marginBottom: '30px',\n        borderBottom: '2px solid #333',\n        paddingBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          color: '#00bcd4',\n          margin: '0 0 10px 0'\n        },\n        children: \"\\uD83E\\uDD16 Simple Robot Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: '10px',\n          fontSize: '14px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: robotData.connected ? '#4CAF50' : '#F44336'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: robotData.connected ? 'CONNECTED' : 'DISCONNECTED'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n        gap: '20px',\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#4CAF50',\n            marginTop: 0\n          },\n          children: \"\\uD83D\\uDCCA Robot Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '5px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Position:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              color: '#aaa'\n            },\n            children: [\"X: \", robotData.position.x.toFixed(2), \"m | Y: \", robotData.position.y.toFixed(2), \"m | Z: \", robotData.position.z.toFixed(2), \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '5px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Velocity:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              color: '#aaa'\n            },\n            children: [\"Linear: \", robotData.velocity.linear.toFixed(2), \"m/s | Angular: \", robotData.velocity.angular.toFixed(2), \"rad/s\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '5px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Battery:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: getBatteryColor(robotData.battery)\n              },\n              children: [robotData.battery.toFixed(1), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100%',\n              height: '8px',\n              backgroundColor: '#333',\n              borderRadius: '4px',\n              overflow: 'hidden'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: `${robotData.battery}%`,\n                height: '100%',\n                backgroundColor: getBatteryColor(robotData.battery),\n                transition: 'width 0.3s ease'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#FF9800',\n            marginTop: 0\n          },\n          children: \"\\uD83D\\uDCF7 Camera Feed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '200px',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444'\n          },\n          children: cameraImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: cameraImage,\n            alt: \"Robot Camera\",\n            style: {\n              maxWidth: '100%',\n              maxHeight: '200px',\n              borderRadius: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#666',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '2rem',\n                marginBottom: '10px'\n              },\n              children: \"\\uD83D\\uDCF7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"No camera feed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#aaa',\n            marginTop: '10px',\n            textAlign: 'center'\n          },\n          children: \"Topic: /camera/image_raw | 10 FPS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#9C27B0',\n            marginTop: 0\n          },\n          children: \"\\uD83D\\uDDFA\\uFE0F Map & Navigation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n            ref: mapCanvasRef,\n            style: {\n              maxWidth: '100%',\n              borderRadius: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#aaa',\n            marginTop: '10px',\n            textAlign: 'center'\n          },\n          children: \"Topic: /map | Robot position tracked\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '20px',\n        borderTop: '1px solid #333',\n        color: '#666',\n        fontSize: '12px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\uD83C\\uDF10 Simple Web-based Robot Visualization\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '5px'\n        },\n        children: \"Real robot data can be connected via WebSocket or REST API\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this);\n};\n_s(RobotDashboard, \"TNIMUMbvPoSHh9tpiLV8yDEEdgk=\");\n_c = RobotDashboard;\nexport default RobotDashboard;\nvar _c;\n$RefreshReg$(_c, \"RobotDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "RobotDashboard", "_s", "robotData", "setRobotData", "position", "x", "y", "z", "orientation", "w", "velocity", "linear", "angular", "battery", "connected", "cameraImage", "setCameraImage", "mapData", "setMapData", "canvasRef", "mapCanvasRef", "interval", "setInterval", "prev", "Math", "random", "max", "clearInterval", "canvas", "document", "createElement", "width", "height", "ctx", "getContext", "gradient", "createLinearGradient", "addColorStop", "fillStyle", "fillRect", "beginPath", "arc", "PI", "fill", "font", "fillText", "Date", "toLocaleTimeString", "toDataURL", "current", "strokeStyle", "lineWidth", "i", "moveTo", "lineTo", "stroke", "robotX", "robotY", "cos", "sin", "getBatteryColor", "level", "style", "background", "minHeight", "color", "fontFamily", "padding", "children", "textAlign", "marginBottom", "borderBottom", "paddingBottom", "margin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "alignItems", "justifyContent", "gap", "fontSize", "borderRadius", "backgroundColor", "gridTemplateColumns", "border", "marginTop", "toFixed", "overflow", "transition", "src", "alt", "max<PERSON><PERSON><PERSON>", "maxHeight", "ref", "borderTop", "_c", "$RefreshReg$"], "sources": ["F:/foxglove/foxglove-panels/src/components/RobotDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\n\ninterface RobotData {\n  position: { x: number; y: number; z: number };\n  orientation: { x: number; y: number; z: number; w: number };\n  velocity: { linear: number; angular: number };\n  battery: number;\n  connected: boolean;\n}\n\nconst RobotDashboard: React.FC = () => {\n  const [robotData, setRobotData] = useState<RobotData>({\n    position: { x: 0, y: 0, z: 0 },\n    orientation: { x: 0, y: 0, z: 0, w: 1 },\n    velocity: { linear: 0, angular: 0 },\n    battery: 85,\n    connected: false\n  });\n\n  const [cameraImage, setCameraImage] = useState<string | null>(null);\n  const [mapData, setMapData] = useState<ImageData | null>(null);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const mapCanvasRef = useRef<HTMLCanvasElement>(null);\n\n  // Simulate robot data updates\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setRobotData(prev => ({\n        ...prev,\n        position: {\n          x: prev.position.x + (Math.random() - 0.5) * 0.1,\n          y: prev.position.y + (Math.random() - 0.5) * 0.1,\n          z: 0\n        },\n        velocity: {\n          linear: Math.random() * 2,\n          angular: (Math.random() - 0.5) * 1\n        },\n        battery: Math.max(0, prev.battery - Math.random() * 0.1),\n        connected: Math.random() > 0.1 // 90% uptime\n      }));\n    }, 1000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  // Simulate camera feed\n  useEffect(() => {\n    const interval = setInterval(() => {\n      // Create a simple simulated camera image\n      const canvas = document.createElement('canvas');\n      canvas.width = 320;\n      canvas.height = 240;\n      const ctx = canvas.getContext('2d');\n      \n      if (ctx) {\n        // Create gradient background\n        const gradient = ctx.createLinearGradient(0, 0, 320, 240);\n        gradient.addColorStop(0, '#1a1a2e');\n        gradient.addColorStop(1, '#16213e');\n        ctx.fillStyle = gradient;\n        ctx.fillRect(0, 0, 320, 240);\n        \n        // Add some \"objects\" to simulate camera view\n        ctx.fillStyle = '#ff6b6b';\n        ctx.fillRect(50 + Math.random() * 100, 50 + Math.random() * 100, 30, 30);\n        \n        ctx.fillStyle = '#4ecdc4';\n        ctx.beginPath();\n        ctx.arc(200 + Math.random() * 50, 120 + Math.random() * 50, 20, 0, 2 * Math.PI);\n        ctx.fill();\n        \n        // Add timestamp\n        ctx.fillStyle = '#fff';\n        ctx.font = '12px monospace';\n        ctx.fillText(new Date().toLocaleTimeString(), 10, 20);\n        \n        setCameraImage(canvas.toDataURL());\n      }\n    }, 100); // 10 FPS\n\n    return () => clearInterval(interval);\n  }, []);\n\n  // Draw simple map\n  useEffect(() => {\n    if (mapCanvasRef.current) {\n      const canvas = mapCanvasRef.current;\n      const ctx = canvas.getContext('2d');\n      \n      if (ctx) {\n        canvas.width = 400;\n        canvas.height = 300;\n        \n        // Clear canvas\n        ctx.fillStyle = '#000';\n        ctx.fillRect(0, 0, 400, 300);\n        \n        // Draw grid\n        ctx.strokeStyle = '#333';\n        ctx.lineWidth = 1;\n        for (let i = 0; i < 400; i += 20) {\n          ctx.beginPath();\n          ctx.moveTo(i, 0);\n          ctx.lineTo(i, 300);\n          ctx.stroke();\n        }\n        for (let i = 0; i < 300; i += 20) {\n          ctx.beginPath();\n          ctx.moveTo(0, i);\n          ctx.lineTo(400, i);\n          ctx.stroke();\n        }\n        \n        // Draw obstacles\n        ctx.fillStyle = '#666';\n        ctx.fillRect(100, 100, 50, 50);\n        ctx.fillRect(250, 150, 30, 80);\n        ctx.fillRect(50, 200, 80, 20);\n        \n        // Draw robot position\n        const robotX = 200 + robotData.position.x * 50;\n        const robotY = 150 + robotData.position.y * 50;\n        \n        ctx.fillStyle = '#ff4444';\n        ctx.beginPath();\n        ctx.arc(robotX, robotY, 8, 0, 2 * Math.PI);\n        ctx.fill();\n        \n        // Draw robot orientation\n        ctx.strokeStyle = '#fff';\n        ctx.lineWidth = 2;\n        ctx.beginPath();\n        ctx.moveTo(robotX, robotY);\n        ctx.lineTo(\n          robotX + Math.cos(0) * 15,\n          robotY + Math.sin(0) * 15\n        );\n        ctx.stroke();\n      }\n    }\n  }, [robotData.position]);\n\n  const getBatteryColor = (level: number): string => {\n    if (level > 50) return '#4CAF50';\n    if (level > 20) return '#FF9800';\n    return '#F44336';\n  };\n\n  return (\n    <div style={{\n      background: '#0d1117',\n      minHeight: '100vh',\n      color: 'white',\n      fontFamily: 'monospace',\n      padding: '20px'\n    }}>\n      {/* Header */}\n      <div style={{\n        textAlign: 'center',\n        marginBottom: '30px',\n        borderBottom: '2px solid #333',\n        paddingBottom: '20px'\n      }}>\n        <h1 style={{ color: '#00bcd4', margin: '0 0 10px 0' }}>\n          🤖 Simple Robot Dashboard\n        </h1>\n        <div style={{ \n          display: 'flex', \n          alignItems: 'center', \n          justifyContent: 'center', \n          gap: '10px',\n          fontSize: '14px'\n        }}>\n          <div style={{\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: robotData.connected ? '#4CAF50' : '#F44336'\n          }}></div>\n          <span>{robotData.connected ? 'CONNECTED' : 'DISCONNECTED'}</span>\n        </div>\n      </div>\n\n      {/* Main Dashboard Grid */}\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n        gap: '20px',\n        marginBottom: '20px'\n      }}>\n        \n        {/* Robot Status */}\n        <div style={{\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        }}>\n          <h3 style={{ color: '#4CAF50', marginTop: 0 }}>📊 Robot Status</h3>\n          \n          <div style={{ marginBottom: '15px' }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\n              <span>Position:</span>\n            </div>\n            <div style={{ fontSize: '12px', color: '#aaa' }}>\n              X: {robotData.position.x.toFixed(2)}m | \n              Y: {robotData.position.y.toFixed(2)}m | \n              Z: {robotData.position.z.toFixed(2)}m\n            </div>\n          </div>\n\n          <div style={{ marginBottom: '15px' }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\n              <span>Velocity:</span>\n            </div>\n            <div style={{ fontSize: '12px', color: '#aaa' }}>\n              Linear: {robotData.velocity.linear.toFixed(2)}m/s | \n              Angular: {robotData.velocity.angular.toFixed(2)}rad/s\n            </div>\n          </div>\n\n          <div style={{ marginBottom: '15px' }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\n              <span>Battery:</span>\n              <span style={{ color: getBatteryColor(robotData.battery) }}>\n                {robotData.battery.toFixed(1)}%\n              </span>\n            </div>\n            <div style={{\n              width: '100%',\n              height: '8px',\n              backgroundColor: '#333',\n              borderRadius: '4px',\n              overflow: 'hidden'\n            }}>\n              <div style={{\n                width: `${robotData.battery}%`,\n                height: '100%',\n                backgroundColor: getBatteryColor(robotData.battery),\n                transition: 'width 0.3s ease'\n              }}></div>\n            </div>\n          </div>\n        </div>\n\n        {/* Camera Feed */}\n        <div style={{\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        }}>\n          <h3 style={{ color: '#FF9800', marginTop: 0 }}>📷 Camera Feed</h3>\n          <div style={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '200px',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444'\n          }}>\n            {cameraImage ? (\n              <img \n                src={cameraImage} \n                alt=\"Robot Camera\" \n                style={{ \n                  maxWidth: '100%', \n                  maxHeight: '200px',\n                  borderRadius: '4px'\n                }} \n              />\n            ) : (\n              <div style={{ color: '#666', textAlign: 'center' }}>\n                <div style={{ fontSize: '2rem', marginBottom: '10px' }}>📷</div>\n                <div>No camera feed</div>\n              </div>\n            )}\n          </div>\n          <div style={{ \n            fontSize: '12px', \n            color: '#aaa', \n            marginTop: '10px',\n            textAlign: 'center'\n          }}>\n            Topic: /camera/image_raw | 10 FPS\n          </div>\n        </div>\n\n        {/* Map Viewer */}\n        <div style={{\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        }}>\n          <h3 style={{ color: '#9C27B0', marginTop: 0 }}>🗺️ Map & Navigation</h3>\n          <div style={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444'\n          }}>\n            <canvas \n              ref={mapCanvasRef}\n              style={{ \n                maxWidth: '100%',\n                borderRadius: '4px'\n              }}\n            />\n          </div>\n          <div style={{ \n            fontSize: '12px', \n            color: '#aaa', \n            marginTop: '10px',\n            textAlign: 'center'\n          }}>\n            Topic: /map | Robot position tracked\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div style={{\n        textAlign: 'center',\n        padding: '20px',\n        borderTop: '1px solid #333',\n        color: '#666',\n        fontSize: '12px'\n      }}>\n        <div>🌐 Simple Web-based Robot Visualization</div>\n        <div style={{ marginTop: '5px' }}>\n          Real robot data can be connected via WebSocket or REST API\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RobotDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAU3D,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAY;IACpDS,QAAQ,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAC9BC,WAAW,EAAE;MAAEH,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC;IACvCC,QAAQ,EAAE;MAAEC,MAAM,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAE,CAAC;IACnCC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAgB,IAAI,CAAC;EACnE,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAmB,IAAI,CAAC;EAC9D,MAAMwB,SAAS,GAAGtB,MAAM,CAAoB,IAAI,CAAC;EACjD,MAAMuB,YAAY,GAAGvB,MAAM,CAAoB,IAAI,CAAC;;EAEpD;EACAD,SAAS,CAAC,MAAM;IACd,MAAMyB,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCnB,YAAY,CAACoB,IAAI,KAAK;QACpB,GAAGA,IAAI;QACPnB,QAAQ,EAAE;UACRC,CAAC,EAAEkB,IAAI,CAACnB,QAAQ,CAACC,CAAC,GAAG,CAACmB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;UAChDnB,CAAC,EAAEiB,IAAI,CAACnB,QAAQ,CAACE,CAAC,GAAG,CAACkB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;UAChDlB,CAAC,EAAE;QACL,CAAC;QACDG,QAAQ,EAAE;UACRC,MAAM,EAAEa,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UACzBb,OAAO,EAAE,CAACY,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI;QACnC,CAAC;QACDZ,OAAO,EAAEW,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACV,OAAO,GAAGW,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;QACxDX,SAAS,EAAEU,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;MACjC,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAME,aAAa,CAACN,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzB,SAAS,CAAC,MAAM;IACd,MAAMyB,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC;MACA,MAAMM,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/CF,MAAM,CAACG,KAAK,GAAG,GAAG;MAClBH,MAAM,CAACI,MAAM,GAAG,GAAG;MACnB,MAAMC,GAAG,GAAGL,MAAM,CAACM,UAAU,CAAC,IAAI,CAAC;MAEnC,IAAID,GAAG,EAAE;QACP;QACA,MAAME,QAAQ,GAAGF,GAAG,CAACG,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;QACzDD,QAAQ,CAACE,YAAY,CAAC,CAAC,EAAE,SAAS,CAAC;QACnCF,QAAQ,CAACE,YAAY,CAAC,CAAC,EAAE,SAAS,CAAC;QACnCJ,GAAG,CAACK,SAAS,GAAGH,QAAQ;QACxBF,GAAG,CAACM,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;;QAE5B;QACAN,GAAG,CAACK,SAAS,GAAG,SAAS;QACzBL,GAAG,CAACM,QAAQ,CAAC,EAAE,GAAGf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE,EAAE,GAAGD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;QAExEQ,GAAG,CAACK,SAAS,GAAG,SAAS;QACzBL,GAAG,CAACO,SAAS,CAAC,CAAC;QACfP,GAAG,CAACQ,GAAG,CAAC,GAAG,GAAGjB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,GAAG,GAAGD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,GAAGD,IAAI,CAACkB,EAAE,CAAC;QAC/ET,GAAG,CAACU,IAAI,CAAC,CAAC;;QAEV;QACAV,GAAG,CAACK,SAAS,GAAG,MAAM;QACtBL,GAAG,CAACW,IAAI,GAAG,gBAAgB;QAC3BX,GAAG,CAACY,QAAQ,CAAC,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;QAErD/B,cAAc,CAACY,MAAM,CAACoB,SAAS,CAAC,CAAC,CAAC;MACpC;IACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAMrB,aAAa,CAACN,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzB,SAAS,CAAC,MAAM;IACd,IAAIwB,YAAY,CAAC6B,OAAO,EAAE;MACxB,MAAMrB,MAAM,GAAGR,YAAY,CAAC6B,OAAO;MACnC,MAAMhB,GAAG,GAAGL,MAAM,CAACM,UAAU,CAAC,IAAI,CAAC;MAEnC,IAAID,GAAG,EAAE;QACPL,MAAM,CAACG,KAAK,GAAG,GAAG;QAClBH,MAAM,CAACI,MAAM,GAAG,GAAG;;QAEnB;QACAC,GAAG,CAACK,SAAS,GAAG,MAAM;QACtBL,GAAG,CAACM,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;;QAE5B;QACAN,GAAG,CAACiB,WAAW,GAAG,MAAM;QACxBjB,GAAG,CAACkB,SAAS,GAAG,CAAC;QACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,IAAI,EAAE,EAAE;UAChCnB,GAAG,CAACO,SAAS,CAAC,CAAC;UACfP,GAAG,CAACoB,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;UAChBnB,GAAG,CAACqB,MAAM,CAACF,CAAC,EAAE,GAAG,CAAC;UAClBnB,GAAG,CAACsB,MAAM,CAAC,CAAC;QACd;QACA,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,IAAI,EAAE,EAAE;UAChCnB,GAAG,CAACO,SAAS,CAAC,CAAC;UACfP,GAAG,CAACoB,MAAM,CAAC,CAAC,EAAED,CAAC,CAAC;UAChBnB,GAAG,CAACqB,MAAM,CAAC,GAAG,EAAEF,CAAC,CAAC;UAClBnB,GAAG,CAACsB,MAAM,CAAC,CAAC;QACd;;QAEA;QACAtB,GAAG,CAACK,SAAS,GAAG,MAAM;QACtBL,GAAG,CAACM,QAAQ,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;QAC9BN,GAAG,CAACM,QAAQ,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;QAC9BN,GAAG,CAACM,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;;QAE7B;QACA,MAAMiB,MAAM,GAAG,GAAG,GAAGtD,SAAS,CAACE,QAAQ,CAACC,CAAC,GAAG,EAAE;QAC9C,MAAMoD,MAAM,GAAG,GAAG,GAAGvD,SAAS,CAACE,QAAQ,CAACE,CAAC,GAAG,EAAE;QAE9C2B,GAAG,CAACK,SAAS,GAAG,SAAS;QACzBL,GAAG,CAACO,SAAS,CAAC,CAAC;QACfP,GAAG,CAACQ,GAAG,CAACe,MAAM,EAAEC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGjC,IAAI,CAACkB,EAAE,CAAC;QAC1CT,GAAG,CAACU,IAAI,CAAC,CAAC;;QAEV;QACAV,GAAG,CAACiB,WAAW,GAAG,MAAM;QACxBjB,GAAG,CAACkB,SAAS,GAAG,CAAC;QACjBlB,GAAG,CAACO,SAAS,CAAC,CAAC;QACfP,GAAG,CAACoB,MAAM,CAACG,MAAM,EAAEC,MAAM,CAAC;QAC1BxB,GAAG,CAACqB,MAAM,CACRE,MAAM,GAAGhC,IAAI,CAACkC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,EACzBD,MAAM,GAAGjC,IAAI,CAACmC,GAAG,CAAC,CAAC,CAAC,GAAG,EACzB,CAAC;QACD1B,GAAG,CAACsB,MAAM,CAAC,CAAC;MACd;IACF;EACF,CAAC,EAAE,CAACrD,SAAS,CAACE,QAAQ,CAAC,CAAC;EAExB,MAAMwD,eAAe,GAAIC,KAAa,IAAa;IACjD,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,SAAS;IAChC,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,SAAS;IAChC,OAAO,SAAS;EAClB,CAAC;EAED,oBACE9D,OAAA;IAAK+D,KAAK,EAAE;MACVC,UAAU,EAAE,SAAS;MACrBC,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,WAAW;MACvBC,OAAO,EAAE;IACX,CAAE;IAAAC,QAAA,gBAEArE,OAAA;MAAK+D,KAAK,EAAE;QACVO,SAAS,EAAE,QAAQ;QACnBC,YAAY,EAAE,MAAM;QACpBC,YAAY,EAAE,gBAAgB;QAC9BC,aAAa,EAAE;MACjB,CAAE;MAAAJ,QAAA,gBACArE,OAAA;QAAI+D,KAAK,EAAE;UAAEG,KAAK,EAAE,SAAS;UAAEQ,MAAM,EAAE;QAAa,CAAE;QAAAL,QAAA,EAAC;MAEvD;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL9E,OAAA;QAAK+D,KAAK,EAAE;UACVgB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,GAAG,EAAE,MAAM;UACXC,QAAQ,EAAE;QACZ,CAAE;QAAAd,QAAA,gBACArE,OAAA;UAAK+D,KAAK,EAAE;YACV/B,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdmD,YAAY,EAAE,KAAK;YACnBC,eAAe,EAAElF,SAAS,CAACY,SAAS,GAAG,SAAS,GAAG;UACrD;QAAE;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACT9E,OAAA;UAAAqE,QAAA,EAAOlE,SAAS,CAACY,SAAS,GAAG,WAAW,GAAG;QAAc;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9E,OAAA;MAAK+D,KAAK,EAAE;QACVgB,OAAO,EAAE,MAAM;QACfO,mBAAmB,EAAE,sCAAsC;QAC3DJ,GAAG,EAAE,MAAM;QACXX,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBAGArE,OAAA;QAAK+D,KAAK,EAAE;UACVsB,eAAe,EAAE,SAAS;UAC1BE,MAAM,EAAE,gBAAgB;UACxBH,YAAY,EAAE,KAAK;UACnBhB,OAAO,EAAE;QACX,CAAE;QAAAC,QAAA,gBACArE,OAAA;UAAI+D,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEsB,SAAS,EAAE;UAAE,CAAE;UAAAnB,QAAA,EAAC;QAAe;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEnE9E,OAAA;UAAK+D,KAAK,EAAE;YAAEQ,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBACnCrE,OAAA;YAAK+D,KAAK,EAAE;cAAEgB,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE,eAAe;cAAEV,YAAY,EAAE;YAAM,CAAE;YAAAF,QAAA,eACpFrE,OAAA;cAAAqE,QAAA,EAAM;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACN9E,OAAA;YAAK+D,KAAK,EAAE;cAAEoB,QAAQ,EAAE,MAAM;cAAEjB,KAAK,EAAE;YAAO,CAAE;YAAAG,QAAA,GAAC,KAC5C,EAAClE,SAAS,CAACE,QAAQ,CAACC,CAAC,CAACmF,OAAO,CAAC,CAAC,CAAC,EAAC,SACjC,EAACtF,SAAS,CAACE,QAAQ,CAACE,CAAC,CAACkF,OAAO,CAAC,CAAC,CAAC,EAAC,SACjC,EAACtF,SAAS,CAACE,QAAQ,CAACG,CAAC,CAACiF,OAAO,CAAC,CAAC,CAAC,EAAC,GACtC;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9E,OAAA;UAAK+D,KAAK,EAAE;YAAEQ,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBACnCrE,OAAA;YAAK+D,KAAK,EAAE;cAAEgB,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE,eAAe;cAAEV,YAAY,EAAE;YAAM,CAAE;YAAAF,QAAA,eACpFrE,OAAA;cAAAqE,QAAA,EAAM;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACN9E,OAAA;YAAK+D,KAAK,EAAE;cAAEoB,QAAQ,EAAE,MAAM;cAAEjB,KAAK,EAAE;YAAO,CAAE;YAAAG,QAAA,GAAC,UACvC,EAAClE,SAAS,CAACQ,QAAQ,CAACC,MAAM,CAAC6E,OAAO,CAAC,CAAC,CAAC,EAAC,iBACrC,EAACtF,SAAS,CAACQ,QAAQ,CAACE,OAAO,CAAC4E,OAAO,CAAC,CAAC,CAAC,EAAC,OAClD;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9E,OAAA;UAAK+D,KAAK,EAAE;YAAEQ,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBACnCrE,OAAA;YAAK+D,KAAK,EAAE;cAAEgB,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE,eAAe;cAAEV,YAAY,EAAE;YAAM,CAAE;YAAAF,QAAA,gBACpFrE,OAAA;cAAAqE,QAAA,EAAM;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrB9E,OAAA;cAAM+D,KAAK,EAAE;gBAAEG,KAAK,EAAEL,eAAe,CAAC1D,SAAS,CAACW,OAAO;cAAE,CAAE;cAAAuD,QAAA,GACxDlE,SAAS,CAACW,OAAO,CAAC2E,OAAO,CAAC,CAAC,CAAC,EAAC,GAChC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN9E,OAAA;YAAK+D,KAAK,EAAE;cACV/B,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,KAAK;cACboD,eAAe,EAAE,MAAM;cACvBD,YAAY,EAAE,KAAK;cACnBM,QAAQ,EAAE;YACZ,CAAE;YAAArB,QAAA,eACArE,OAAA;cAAK+D,KAAK,EAAE;gBACV/B,KAAK,EAAE,GAAG7B,SAAS,CAACW,OAAO,GAAG;gBAC9BmB,MAAM,EAAE,MAAM;gBACdoD,eAAe,EAAExB,eAAe,CAAC1D,SAAS,CAACW,OAAO,CAAC;gBACnD6E,UAAU,EAAE;cACd;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9E,OAAA;QAAK+D,KAAK,EAAE;UACVsB,eAAe,EAAE,SAAS;UAC1BE,MAAM,EAAE,gBAAgB;UACxBH,YAAY,EAAE,KAAK;UACnBhB,OAAO,EAAE;QACX,CAAE;QAAAC,QAAA,gBACArE,OAAA;UAAI+D,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEsB,SAAS,EAAE;UAAE,CAAE;UAAAnB,QAAA,EAAC;QAAc;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClE9E,OAAA;UAAK+D,KAAK,EAAE;YACVgB,OAAO,EAAE,MAAM;YACfE,cAAc,EAAE,QAAQ;YACxBD,UAAU,EAAE,QAAQ;YACpBf,SAAS,EAAE,OAAO;YAClBoB,eAAe,EAAE,MAAM;YACvBD,YAAY,EAAE,KAAK;YACnBG,MAAM,EAAE;UACV,CAAE;UAAAlB,QAAA,EACCrD,WAAW,gBACVhB,OAAA;YACE4F,GAAG,EAAE5E,WAAY;YACjB6E,GAAG,EAAC,cAAc;YAClB9B,KAAK,EAAE;cACL+B,QAAQ,EAAE,MAAM;cAChBC,SAAS,EAAE,OAAO;cAClBX,YAAY,EAAE;YAChB;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEF9E,OAAA;YAAK+D,KAAK,EAAE;cAAEG,KAAK,EAAE,MAAM;cAAEI,SAAS,EAAE;YAAS,CAAE;YAAAD,QAAA,gBACjDrE,OAAA;cAAK+D,KAAK,EAAE;gBAAEoB,QAAQ,EAAE,MAAM;gBAAEZ,YAAY,EAAE;cAAO,CAAE;cAAAF,QAAA,EAAC;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChE9E,OAAA;cAAAqE,QAAA,EAAK;YAAc;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN9E,OAAA;UAAK+D,KAAK,EAAE;YACVoB,QAAQ,EAAE,MAAM;YAChBjB,KAAK,EAAE,MAAM;YACbsB,SAAS,EAAE,MAAM;YACjBlB,SAAS,EAAE;UACb,CAAE;UAAAD,QAAA,EAAC;QAEH;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9E,OAAA;QAAK+D,KAAK,EAAE;UACVsB,eAAe,EAAE,SAAS;UAC1BE,MAAM,EAAE,gBAAgB;UACxBH,YAAY,EAAE,KAAK;UACnBhB,OAAO,EAAE;QACX,CAAE;QAAAC,QAAA,gBACArE,OAAA;UAAI+D,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEsB,SAAS,EAAE;UAAE,CAAE;UAAAnB,QAAA,EAAC;QAAoB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxE9E,OAAA;UAAK+D,KAAK,EAAE;YACVgB,OAAO,EAAE,MAAM;YACfE,cAAc,EAAE,QAAQ;YACxBD,UAAU,EAAE,QAAQ;YACpBK,eAAe,EAAE,MAAM;YACvBD,YAAY,EAAE,KAAK;YACnBG,MAAM,EAAE;UACV,CAAE;UAAAlB,QAAA,eACArE,OAAA;YACEgG,GAAG,EAAE3E,YAAa;YAClB0C,KAAK,EAAE;cACL+B,QAAQ,EAAE,MAAM;cAChBV,YAAY,EAAE;YAChB;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9E,OAAA;UAAK+D,KAAK,EAAE;YACVoB,QAAQ,EAAE,MAAM;YAChBjB,KAAK,EAAE,MAAM;YACbsB,SAAS,EAAE,MAAM;YACjBlB,SAAS,EAAE;UACb,CAAE;UAAAD,QAAA,EAAC;QAEH;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9E,OAAA;MAAK+D,KAAK,EAAE;QACVO,SAAS,EAAE,QAAQ;QACnBF,OAAO,EAAE,MAAM;QACf6B,SAAS,EAAE,gBAAgB;QAC3B/B,KAAK,EAAE,MAAM;QACbiB,QAAQ,EAAE;MACZ,CAAE;MAAAd,QAAA,gBACArE,OAAA;QAAAqE,QAAA,EAAK;MAAuC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClD9E,OAAA;QAAK+D,KAAK,EAAE;UAAEyB,SAAS,EAAE;QAAM,CAAE;QAAAnB,QAAA,EAAC;MAElC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5E,EAAA,CA1UID,cAAwB;AAAAiG,EAAA,GAAxBjG,cAAwB;AA4U9B,eAAeA,cAAc;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}