{"ast": null, "code": "var _jsxFileName = \"F:\\\\foxglove\\\\foxglove-panels\\\\src\\\\components\\\\RobotDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport FoxgloveStudioLink from './FoxgloveStudioLink';\n\n// Configuration - Change these URLs to match your setup\n// Option 1: ROSBridge WebSocket (default)\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ROS_WEBSOCKET_URL = 'ws://192.168.1.192:9090';\n// Option 2: Foxglove Bridge WebSocket (uncomment line below and comment line above)\n// const ROS_WEBSOCKET_URL = 'ws://192.168.1.192:8765';\n\nconst RobotDashboard = () => {\n  _s();\n  var _sensorData$, _sensorData$$ranges, _sensorData$2, _sensorData$2$range_m, _sensorData$3, _sensorData$3$range_m, _sensorData$4, _sensorData$5;\n  const [robotData, setRobotData] = useState({\n    position: {\n      x: 0,\n      y: 0,\n      z: 0\n    },\n    orientation: {\n      x: 0,\n      y: 0,\n      z: 0,\n      w: 1\n    },\n    velocity: {\n      linear: 0,\n      angular: 0\n    },\n    battery: 0,\n    // Will be updated from real battery data\n    connected: false\n  });\n  const [plotDataCount, setPlotDataCount] = useState(0);\n  const [tfMessages, setTfMessages] = useState([]);\n  const [sensorData, setSensorData] = useState([]);\n  const [cameraImage, setCameraImage] = useState(null);\n\n  // Connect to ROS2 via ROSBridge WebSocket\n  useEffect(() => {\n    const wsUrl = ROS_WEBSOCKET_URL;\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('Connected to ROS2 WebSocket');\n      setRobotData(prev => ({\n        ...prev,\n        connected: true\n      }));\n\n      // Subscribe to odometry topic\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/odom',\n        type: 'nav_msgs/Odometry'\n      }));\n\n      // Subscribe to battery state (if available)\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/battery_state',\n        type: 'sensor_msgs/BatteryState'\n      }));\n\n      // Subscribe to cmd_vel to monitor velocity commands\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/cmd_vel',\n        type: 'geometry_msgs/Twist'\n      }));\n\n      // Subscribe to tf for transform data (like Webviz)\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/tf',\n        type: 'tf2_msgs/TFMessage'\n      }));\n\n      // Subscribe to scan data for 3D visualization\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/scan',\n        type: 'sensor_msgs/LaserScan'\n      }));\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n        if (message.topic === '/odom' && message.msg) {\n          const pose = message.msg.pose.pose;\n          const twist = message.msg.twist.twist;\n          setRobotData(prev => ({\n            ...prev,\n            position: {\n              x: pose.position.x,\n              y: pose.position.y,\n              z: pose.position.z\n            },\n            orientation: {\n              x: pose.orientation.x,\n              y: pose.orientation.y,\n              z: pose.orientation.z,\n              w: pose.orientation.w\n            },\n            velocity: {\n              linear: Math.sqrt(twist.linear.x ** 2 + twist.linear.y ** 2),\n              angular: twist.angular.z\n            }\n          }));\n\n          // Count received data points\n          setPlotDataCount(prev => prev + 1);\n        }\n        if (message.topic === '/tf' && message.msg) {\n          // Store TF messages (like Webviz Raw Messages panel)\n          setTfMessages(prev => [message.msg, ...prev.slice(0, 9)]); // Keep last 10 messages\n        }\n        if (message.topic === '/scan' && message.msg) {\n          // Store sensor data for 3D visualization\n          setSensorData(prev => [message.msg, ...prev.slice(0, 4)]); // Keep last 5 scans\n        }\n        if (message.topic === '/battery_state' && message.msg) {\n          setRobotData(prev => ({\n            ...prev,\n            battery: message.msg.percentage * 100\n          }));\n        }\n      } catch (error) {\n        console.error('Error parsing WebSocket message:', error);\n      }\n    };\n    ws.onclose = () => {\n      console.log('Disconnected from ROS2 WebSocket');\n      setRobotData(prev => ({\n        ...prev,\n        connected: false\n      }));\n    };\n    ws.onerror = error => {\n      console.error('WebSocket error:', error);\n      setRobotData(prev => ({\n        ...prev,\n        connected: false\n      }));\n    };\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n  // Connect to camera feed\n  useEffect(() => {\n    // Connect to camera topic via WebSocket\n    const wsUrl = ROS_WEBSOCKET_URL;\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      // Subscribe to camera topic\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/camera/image_raw',\n        type: 'sensor_msgs/Image'\n      }));\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n        if (message.topic === '/camera/image_raw' && message.msg) {\n          // Convert ROS image message to displayable format\n          const imageMsg = message.msg;\n          if (imageMsg.encoding === 'rgb8' || imageMsg.encoding === 'bgr8') {\n            // Create canvas to display image\n            const canvas = document.createElement('canvas');\n            canvas.width = imageMsg.width;\n            canvas.height = imageMsg.height;\n            const ctx = canvas.getContext('2d');\n            if (ctx) {\n              // Convert base64 data to image\n              const imageData = ctx.createImageData(imageMsg.width, imageMsg.height);\n              const data = atob(imageMsg.data);\n              for (let i = 0; i < data.length; i += 3) {\n                const pixelIndex = i / 3 * 4;\n                if (imageMsg.encoding === 'rgb8') {\n                  imageData.data[pixelIndex] = data.charCodeAt(i); // R\n                  imageData.data[pixelIndex + 1] = data.charCodeAt(i + 1); // G\n                  imageData.data[pixelIndex + 2] = data.charCodeAt(i + 2); // B\n                } else {\n                  // bgr8\n                  imageData.data[pixelIndex] = data.charCodeAt(i + 2); // R\n                  imageData.data[pixelIndex + 1] = data.charCodeAt(i + 1); // G\n                  imageData.data[pixelIndex + 2] = data.charCodeAt(i); // B\n                }\n                imageData.data[pixelIndex + 3] = 255; // A\n              }\n              ctx.putImageData(imageData, 0, 0);\n              setCameraImage(canvas.toDataURL());\n            }\n          }\n        }\n      } catch (error) {\n        console.error('Error processing camera message:', error);\n      }\n    };\n    return () => {\n      ws.close();\n    };\n  }, []);\n  const getBatteryColor = level => {\n    if (level > 50) return '#4CAF50';\n    if (level > 20) return '#FF9800';\n    return '#F44336';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: '#0d1117',\n      minHeight: '100vh',\n      color: 'white',\n      fontFamily: 'monospace',\n      padding: '20px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        marginBottom: '30px',\n        borderBottom: '2px solid #333',\n        paddingBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          color: '#00bcd4',\n          margin: '0 0 10px 0'\n        },\n        children: \"\\uD83E\\uDD16 TurtleBot3 Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: '10px',\n          fontSize: '14px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: robotData.connected ? '#4CAF50' : '#F44336'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: robotData.connected ? 'CONNECTED TO ROS2' : 'DISCONNECTED'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            marginLeft: '20px',\n            fontSize: '12px',\n            color: '#666'\n          },\n          children: \"WebSocket: ws://localhost:9090 (ROSBridge)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n        gap: '20px',\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#00bcd4',\n            marginTop: 0\n          },\n          children: \"\\uD83D\\uDCC8 Position Data (/odom)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444',\n            padding: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '10px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              style: {\n                color: '#00bcd4'\n              },\n              children: \"Current Position:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '14px',\n              color: '#aaa',\n              marginBottom: '8px'\n            },\n            children: [\"X: \", robotData.position.x.toFixed(3), \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '14px',\n              color: '#aaa',\n              marginBottom: '8px'\n            },\n            children: [\"Y: \", robotData.position.y.toFixed(3), \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '14px',\n              color: '#aaa'\n            },\n            children: [\"Z: \", robotData.position.z.toFixed(3), \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#aaa',\n            marginTop: '10px'\n          },\n          children: [\"Data points received: \", plotDataCount]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#FF9800',\n            marginTop: 0\n          },\n          children: \"\\uD83D\\uDCCB Raw Messages (/tf)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '200px',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444',\n            padding: '10px',\n            overflow: 'auto',\n            fontFamily: 'monospace',\n            fontSize: '11px'\n          },\n          children: tfMessages.length > 0 ? tfMessages.map((msg, index) => {\n            var _msg$transforms, _msg$transforms$, _msg$transforms$$head, _msg$transforms2, _msg$transforms2$, _msg$transforms3, _msg$transforms3$, _msg$transforms$0$tra, _msg$transforms$0$tra2, _msg$transforms$0$tra3;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '8px',\n                color: index === 0 ? '#00ff00' : '#aaa',\n                borderBottom: '1px solid #333',\n                paddingBottom: '4px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#00bcd4'\n                },\n                children: [\"Transform #\", index + 1, \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Frames: \", ((_msg$transforms = msg.transforms) === null || _msg$transforms === void 0 ? void 0 : (_msg$transforms$ = _msg$transforms[0]) === null || _msg$transforms$ === void 0 ? void 0 : (_msg$transforms$$head = _msg$transforms$.header) === null || _msg$transforms$$head === void 0 ? void 0 : _msg$transforms$$head.frame_id) || 'N/A', \" \\u2192 \", ((_msg$transforms2 = msg.transforms) === null || _msg$transforms2 === void 0 ? void 0 : (_msg$transforms2$ = _msg$transforms2[0]) === null || _msg$transforms2$ === void 0 ? void 0 : _msg$transforms2$.child_frame_id) || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this), ((_msg$transforms3 = msg.transforms) === null || _msg$transforms3 === void 0 ? void 0 : (_msg$transforms3$ = _msg$transforms3[0]) === null || _msg$transforms3$ === void 0 ? void 0 : _msg$transforms3$.transform) && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Translation: [\", (_msg$transforms$0$tra = msg.transforms[0].transform.translation.x) === null || _msg$transforms$0$tra === void 0 ? void 0 : _msg$transforms$0$tra.toFixed(3), \", \", (_msg$transforms$0$tra2 = msg.transforms[0].transform.translation.y) === null || _msg$transforms$0$tra2 === void 0 ? void 0 : _msg$transforms$0$tra2.toFixed(3), \", \", (_msg$transforms$0$tra3 = msg.transforms[0].transform.translation.z) === null || _msg$transforms$0$tra3 === void 0 ? void 0 : _msg$transforms$0$tra3.toFixed(3), \"]\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 17\n            }, this);\n          }) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#666',\n              textAlign: 'center',\n              marginTop: '80px'\n            },\n            children: \"Waiting for /tf messages...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#aaa',\n            marginTop: '10px'\n          },\n          children: [\"Messages received: \", tfMessages.length, \" | Topic: /tf\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#4CAF50',\n            marginTop: 0\n          },\n          children: \"\\uD83D\\uDCCA Robot Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '5px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Position:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              color: '#aaa'\n            },\n            children: [\"X: \", robotData.position.x.toFixed(2), \"m | Y: \", robotData.position.y.toFixed(2), \"m | Z: \", robotData.position.z.toFixed(2), \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '5px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Velocity:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              color: '#aaa'\n            },\n            children: [\"Linear: \", robotData.velocity.linear.toFixed(2), \"m/s | Angular: \", robotData.velocity.angular.toFixed(2), \"rad/s\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              marginBottom: '5px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Battery:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: getBatteryColor(robotData.battery)\n              },\n              children: [robotData.battery.toFixed(1), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100%',\n              height: '8px',\n              backgroundColor: '#333',\n              borderRadius: '4px',\n              overflow: 'hidden'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: `${robotData.battery}%`,\n                height: '100%',\n                backgroundColor: getBatteryColor(robotData.battery),\n                transition: 'width 0.3s ease'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#FF9800',\n            marginTop: 0\n          },\n          children: \"\\uD83D\\uDCF7 Camera Feed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '200px',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444'\n          },\n          children: cameraImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: cameraImage,\n            alt: \"Robot Camera\",\n            style: {\n              maxWidth: '100%',\n              maxHeight: '200px',\n              borderRadius: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#666',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '2rem',\n                marginBottom: '10px'\n              },\n              children: \"\\uD83D\\uDCF7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"No camera feed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#aaa',\n            marginTop: '10px',\n            textAlign: 'center'\n          },\n          children: \"Topic: /camera/image_raw | 10 FPS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#E91E63',\n            marginTop: 0\n          },\n          children: \"\\uD83C\\uDF10 Sensor Data (/scan)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444',\n            padding: '15px'\n          },\n          children: sensorData.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '10px'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                style: {\n                  color: '#E91E63'\n                },\n                children: \"Latest Scan:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '14px',\n                color: '#aaa',\n                marginBottom: '8px'\n              },\n              children: [\"Points: \", ((_sensorData$ = sensorData[0]) === null || _sensorData$ === void 0 ? void 0 : (_sensorData$$ranges = _sensorData$.ranges) === null || _sensorData$$ranges === void 0 ? void 0 : _sensorData$$ranges.length) || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '14px',\n                color: '#aaa',\n                marginBottom: '8px'\n              },\n              children: [\"Range: \", ((_sensorData$2 = sensorData[0]) === null || _sensorData$2 === void 0 ? void 0 : (_sensorData$2$range_m = _sensorData$2.range_min) === null || _sensorData$2$range_m === void 0 ? void 0 : _sensorData$2$range_m.toFixed(2)) || 'N/A', \"m - \", ((_sensorData$3 = sensorData[0]) === null || _sensorData$3 === void 0 ? void 0 : (_sensorData$3$range_m = _sensorData$3.range_max) === null || _sensorData$3$range_m === void 0 ? void 0 : _sensorData$3$range_m.toFixed(1)) || 'N/A', \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '14px',\n                color: '#aaa'\n              },\n              children: [\"Angle: \", ((((_sensorData$4 = sensorData[0]) === null || _sensorData$4 === void 0 ? void 0 : _sensorData$4.angle_min) || 0) * 180 / Math.PI).toFixed(1), \"\\xB0 - \", ((((_sensorData$5 = sensorData[0]) === null || _sensorData$5 === void 0 ? void 0 : _sensorData$5.angle_max) || 0) * 180 / Math.PI).toFixed(1), \"\\xB0\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#666',\n              textAlign: 'center',\n              padding: '20px'\n            },\n            children: \"Waiting for sensor data...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#aaa',\n            marginTop: '10px'\n          },\n          children: [\"Scans received: \", sensorData.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#9C27B0',\n            marginTop: 0\n          },\n          children: \"\\uD83D\\uDDFA\\uFE0F Map Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444',\n            padding: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '10px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              style: {\n                color: '#9C27B0'\n              },\n              children: \"Map Topic:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '14px',\n              color: '#aaa',\n              marginBottom: '8px'\n            },\n            children: \"Topic: /map\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '14px',\n              color: '#aaa',\n              marginBottom: '8px'\n            },\n            children: [\"Status: \", robotData.connected ? 'Connected' : 'Disconnected']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '14px',\n              color: '#aaa'\n            },\n            children: \"Use Foxglove Studio below for map visualization\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FoxgloveStudioLink, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 517,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '20px',\n        borderTop: '1px solid #333',\n        color: '#666',\n        fontSize: '12px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\uD83C\\uDF10 TurtleBot3 Webviz-Style Dashboard via ROSBridge\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '5px'\n        },\n        children: [\"Topics: /odom (plot), /tf (raw messages), /scan (3D), /camera/image_raw, /map | WebSocket: \", ROS_WEBSOCKET_URL]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 520,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 221,\n    columnNumber: 5\n  }, this);\n};\n_s(RobotDashboard, \"eQqAe8JO/hc9pnaiQ1pWgdpknD8=\");\n_c = RobotDashboard;\nexport default RobotDashboard;\nvar _c;\n$RefreshReg$(_c, \"RobotDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FoxgloveStudioLink", "jsxDEV", "_jsxDEV", "ROS_WEBSOCKET_URL", "RobotDashboard", "_s", "_sensorData$", "_sensorData$$ranges", "_sensorData$2", "_sensorData$2$range_m", "_sensorData$3", "_sensorData$3$range_m", "_sensorData$4", "_sensorData$5", "robotData", "setRobotData", "position", "x", "y", "z", "orientation", "w", "velocity", "linear", "angular", "battery", "connected", "plotDataCount", "setPlotDataCount", "tfMessages", "setTfMessages", "sensorData", "setSensorData", "cameraImage", "setCameraImage", "wsUrl", "ws", "WebSocket", "onopen", "console", "log", "prev", "send", "JSON", "stringify", "op", "topic", "type", "onmessage", "event", "message", "parse", "data", "msg", "pose", "twist", "Math", "sqrt", "slice", "percentage", "error", "onclose", "onerror", "close", "imageMsg", "encoding", "canvas", "document", "createElement", "width", "height", "ctx", "getContext", "imageData", "createImageData", "atob", "i", "length", "pixelIndex", "charCodeAt", "putImageData", "toDataURL", "getBatteryColor", "level", "style", "background", "minHeight", "color", "fontFamily", "padding", "children", "textAlign", "marginBottom", "borderBottom", "paddingBottom", "margin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "alignItems", "justifyContent", "gap", "fontSize", "borderRadius", "backgroundColor", "marginLeft", "gridTemplateColumns", "border", "marginTop", "toFixed", "overflow", "map", "index", "_msg$transforms", "_msg$transforms$", "_msg$transforms$$head", "_msg$transforms2", "_msg$transforms2$", "_msg$transforms3", "_msg$transforms3$", "_msg$transforms$0$tra", "_msg$transforms$0$tra2", "_msg$transforms$0$tra3", "transforms", "header", "frame_id", "child_frame_id", "transform", "translation", "transition", "src", "alt", "max<PERSON><PERSON><PERSON>", "maxHeight", "ranges", "range_min", "range_max", "angle_min", "PI", "angle_max", "borderTop", "_c", "$RefreshReg$"], "sources": ["F:/foxglove/foxglove-panels/src/components/RobotDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport FoxgloveStudioLink from './FoxgloveStudioLink';\n\n// Configuration - Change these URLs to match your setup\n// Option 1: ROSBridge WebSocket (default)\nconst ROS_WEBSOCKET_URL = 'ws://192.168.1.192:9090';\n// Option 2: Foxglove Bridge WebSocket (uncomment line below and comment line above)\n// const ROS_WEBSOCKET_URL = 'ws://192.168.1.192:8765';\n\ninterface RobotData {\n  position: { x: number; y: number; z: number };\n  orientation: { x: number; y: number; z: number; w: number };\n  velocity: { linear: number; angular: number };\n  battery: number;\n  connected: boolean;\n}\n\n\n\nconst RobotDashboard: React.FC = () => {\n  const [robotData, setRobotData] = useState<RobotData>({\n    position: { x: 0, y: 0, z: 0 },\n    orientation: { x: 0, y: 0, z: 0, w: 1 },\n    velocity: { linear: 0, angular: 0 },\n    battery: 0, // Will be updated from real battery data\n    connected: false\n  });\n\n  const [plotDataCount, setPlotDataCount] = useState<number>(0);\n  const [tfMessages, setTfMessages] = useState<any[]>([]);\n  const [sensorData, setSensorData] = useState<any[]>([]);\n  const [cameraImage, setCameraImage] = useState<string | null>(null);\n\n  // Connect to ROS2 via ROSBridge WebSocket\n  useEffect(() => {\n    const wsUrl = ROS_WEBSOCKET_URL;\n\n    const ws = new WebSocket(wsUrl);\n\n    ws.onopen = () => {\n      console.log('Connected to ROS2 WebSocket');\n      setRobotData(prev => ({ ...prev, connected: true }));\n\n      // Subscribe to odometry topic\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/odom',\n        type: 'nav_msgs/Odometry'\n      }));\n\n      // Subscribe to battery state (if available)\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/battery_state',\n        type: 'sensor_msgs/BatteryState'\n      }));\n\n      // Subscribe to cmd_vel to monitor velocity commands\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/cmd_vel',\n        type: 'geometry_msgs/Twist'\n      }));\n\n      // Subscribe to tf for transform data (like Webviz)\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/tf',\n        type: 'tf2_msgs/TFMessage'\n      }));\n\n      // Subscribe to scan data for 3D visualization\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/scan',\n        type: 'sensor_msgs/LaserScan'\n      }));\n    };\n\n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n\n        if (message.topic === '/odom' && message.msg) {\n          const pose = message.msg.pose.pose;\n          const twist = message.msg.twist.twist;\n\n          setRobotData(prev => ({\n            ...prev,\n            position: {\n              x: pose.position.x,\n              y: pose.position.y,\n              z: pose.position.z\n            },\n            orientation: {\n              x: pose.orientation.x,\n              y: pose.orientation.y,\n              z: pose.orientation.z,\n              w: pose.orientation.w\n            },\n            velocity: {\n              linear: Math.sqrt(twist.linear.x ** 2 + twist.linear.y ** 2),\n              angular: twist.angular.z\n            }\n          }));\n\n          // Count received data points\n          setPlotDataCount(prev => prev + 1);\n        }\n\n        if (message.topic === '/tf' && message.msg) {\n          // Store TF messages (like Webviz Raw Messages panel)\n          setTfMessages(prev => [message.msg, ...prev.slice(0, 9)]); // Keep last 10 messages\n        }\n\n        if (message.topic === '/scan' && message.msg) {\n          // Store sensor data for 3D visualization\n          setSensorData(prev => [message.msg, ...prev.slice(0, 4)]); // Keep last 5 scans\n        }\n\n        if (message.topic === '/battery_state' && message.msg) {\n          setRobotData(prev => ({\n            ...prev,\n            battery: message.msg.percentage * 100\n          }));\n        }\n\n      } catch (error) {\n        console.error('Error parsing WebSocket message:', error);\n      }\n    };\n\n    ws.onclose = () => {\n      console.log('Disconnected from ROS2 WebSocket');\n      setRobotData(prev => ({ ...prev, connected: false }));\n    };\n\n    ws.onerror = (error) => {\n      console.error('WebSocket error:', error);\n      setRobotData(prev => ({ ...prev, connected: false }));\n    };\n\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n  // Connect to camera feed\n  useEffect(() => {\n    // Connect to camera topic via WebSocket\n    const wsUrl = ROS_WEBSOCKET_URL;\n    const ws = new WebSocket(wsUrl);\n\n    ws.onopen = () => {\n      // Subscribe to camera topic\n      ws.send(JSON.stringify({\n        op: 'subscribe',\n        topic: '/camera/image_raw',\n        type: 'sensor_msgs/Image'\n      }));\n    };\n\n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n\n        if (message.topic === '/camera/image_raw' && message.msg) {\n          // Convert ROS image message to displayable format\n          const imageMsg = message.msg;\n\n          if (imageMsg.encoding === 'rgb8' || imageMsg.encoding === 'bgr8') {\n            // Create canvas to display image\n            const canvas = document.createElement('canvas');\n            canvas.width = imageMsg.width;\n            canvas.height = imageMsg.height;\n            const ctx = canvas.getContext('2d');\n\n            if (ctx) {\n              // Convert base64 data to image\n              const imageData = ctx.createImageData(imageMsg.width, imageMsg.height);\n              const data = atob(imageMsg.data);\n\n              for (let i = 0; i < data.length; i += 3) {\n                const pixelIndex = (i / 3) * 4;\n                if (imageMsg.encoding === 'rgb8') {\n                  imageData.data[pixelIndex] = data.charCodeAt(i);     // R\n                  imageData.data[pixelIndex + 1] = data.charCodeAt(i + 1); // G\n                  imageData.data[pixelIndex + 2] = data.charCodeAt(i + 2); // B\n                } else { // bgr8\n                  imageData.data[pixelIndex] = data.charCodeAt(i + 2);     // R\n                  imageData.data[pixelIndex + 1] = data.charCodeAt(i + 1); // G\n                  imageData.data[pixelIndex + 2] = data.charCodeAt(i);     // B\n                }\n                imageData.data[pixelIndex + 3] = 255; // A\n              }\n\n              ctx.putImageData(imageData, 0, 0);\n              setCameraImage(canvas.toDataURL());\n            }\n          }\n        }\n      } catch (error) {\n        console.error('Error processing camera message:', error);\n      }\n    };\n\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n\n\n  const getBatteryColor = (level: number): string => {\n    if (level > 50) return '#4CAF50';\n    if (level > 20) return '#FF9800';\n    return '#F44336';\n  };\n\n  return (\n    <div style={{\n      background: '#0d1117',\n      minHeight: '100vh',\n      color: 'white',\n      fontFamily: 'monospace',\n      padding: '20px'\n    }}>\n      {/* Header */}\n      <div style={{\n        textAlign: 'center',\n        marginBottom: '30px',\n        borderBottom: '2px solid #333',\n        paddingBottom: '20px'\n      }}>\n        <h1 style={{ color: '#00bcd4', margin: '0 0 10px 0' }}>\n          🤖 TurtleBot3 Dashboard\n        </h1>\n        <div style={{ \n          display: 'flex', \n          alignItems: 'center', \n          justifyContent: 'center', \n          gap: '10px',\n          fontSize: '14px'\n        }}>\n          <div style={{\n            width: '12px',\n            height: '12px',\n            borderRadius: '50%',\n            backgroundColor: robotData.connected ? '#4CAF50' : '#F44336'\n          }}></div>\n          <span>{robotData.connected ? 'CONNECTED TO ROS2' : 'DISCONNECTED'}</span>\n          <span style={{ marginLeft: '20px', fontSize: '12px', color: '#666' }}>\n            WebSocket: ws://localhost:9090 (ROSBridge)\n          </span>\n        </div>\n      </div>\n\n      {/* Main Dashboard Grid - Webviz Style Layout */}\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n        gap: '20px',\n        marginBottom: '20px'\n      }}>\n\n        {/* Position Data Display */}\n        <div style={{\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        }}>\n          <h3 style={{ color: '#00bcd4', marginTop: 0 }}>📈 Position Data (/odom)</h3>\n          <div style={{\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444',\n            padding: '15px'\n          }}>\n            <div style={{ marginBottom: '10px' }}>\n              <strong style={{ color: '#00bcd4' }}>Current Position:</strong>\n            </div>\n            <div style={{ fontSize: '14px', color: '#aaa', marginBottom: '8px' }}>\n              X: {robotData.position.x.toFixed(3)}m\n            </div>\n            <div style={{ fontSize: '14px', color: '#aaa', marginBottom: '8px' }}>\n              Y: {robotData.position.y.toFixed(3)}m\n            </div>\n            <div style={{ fontSize: '14px', color: '#aaa' }}>\n              Z: {robotData.position.z.toFixed(3)}m\n            </div>\n          </div>\n          <div style={{ fontSize: '12px', color: '#aaa', marginTop: '10px' }}>\n            Data points received: {plotDataCount}\n          </div>\n        </div>\n        \n        {/* Raw Messages Panel - TF Messages (like Webviz) */}\n        <div style={{\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        }}>\n          <h3 style={{ color: '#FF9800', marginTop: 0 }}>📋 Raw Messages (/tf)</h3>\n          <div style={{\n            height: '200px',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444',\n            padding: '10px',\n            overflow: 'auto',\n            fontFamily: 'monospace',\n            fontSize: '11px'\n          }}>\n            {tfMessages.length > 0 ? (\n              tfMessages.map((msg, index) => (\n                <div key={index} style={{\n                  marginBottom: '8px',\n                  color: index === 0 ? '#00ff00' : '#aaa',\n                  borderBottom: '1px solid #333',\n                  paddingBottom: '4px'\n                }}>\n                  <div style={{ color: '#00bcd4' }}>\n                    Transform #{index + 1}:\n                  </div>\n                  <div>\n                    Frames: {msg.transforms?.[0]?.header?.frame_id || 'N/A'} → {msg.transforms?.[0]?.child_frame_id || 'N/A'}\n                  </div>\n                  {msg.transforms?.[0]?.transform && (\n                    <div>\n                      Translation: [{msg.transforms[0].transform.translation.x?.toFixed(3)}, {msg.transforms[0].transform.translation.y?.toFixed(3)}, {msg.transforms[0].transform.translation.z?.toFixed(3)}]\n                    </div>\n                  )}\n                </div>\n              ))\n            ) : (\n              <div style={{ color: '#666', textAlign: 'center', marginTop: '80px' }}>\n                Waiting for /tf messages...\n              </div>\n            )}\n          </div>\n          <div style={{ fontSize: '12px', color: '#aaa', marginTop: '10px' }}>\n            Messages received: {tfMessages.length} | Topic: /tf\n          </div>\n        </div>\n\n        {/* Robot Status */}\n        <div style={{\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        }}>\n          <h3 style={{ color: '#4CAF50', marginTop: 0 }}>📊 Robot Status</h3>\n          \n          <div style={{ marginBottom: '15px' }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\n              <span>Position:</span>\n            </div>\n            <div style={{ fontSize: '12px', color: '#aaa' }}>\n              X: {robotData.position.x.toFixed(2)}m | \n              Y: {robotData.position.y.toFixed(2)}m | \n              Z: {robotData.position.z.toFixed(2)}m\n            </div>\n          </div>\n\n          <div style={{ marginBottom: '15px' }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\n              <span>Velocity:</span>\n            </div>\n            <div style={{ fontSize: '12px', color: '#aaa' }}>\n              Linear: {robotData.velocity.linear.toFixed(2)}m/s | \n              Angular: {robotData.velocity.angular.toFixed(2)}rad/s\n            </div>\n          </div>\n\n          <div style={{ marginBottom: '15px' }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>\n              <span>Battery:</span>\n              <span style={{ color: getBatteryColor(robotData.battery) }}>\n                {robotData.battery.toFixed(1)}%\n              </span>\n            </div>\n            <div style={{\n              width: '100%',\n              height: '8px',\n              backgroundColor: '#333',\n              borderRadius: '4px',\n              overflow: 'hidden'\n            }}>\n              <div style={{\n                width: `${robotData.battery}%`,\n                height: '100%',\n                backgroundColor: getBatteryColor(robotData.battery),\n                transition: 'width 0.3s ease'\n              }}></div>\n            </div>\n          </div>\n        </div>\n\n        {/* Camera Feed */}\n        <div style={{\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        }}>\n          <h3 style={{ color: '#FF9800', marginTop: 0 }}>📷 Camera Feed</h3>\n          <div style={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '200px',\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444'\n          }}>\n            {cameraImage ? (\n              <img \n                src={cameraImage} \n                alt=\"Robot Camera\" \n                style={{ \n                  maxWidth: '100%', \n                  maxHeight: '200px',\n                  borderRadius: '4px'\n                }} \n              />\n            ) : (\n              <div style={{ color: '#666', textAlign: 'center' }}>\n                <div style={{ fontSize: '2rem', marginBottom: '10px' }}>📷</div>\n                <div>No camera feed</div>\n              </div>\n            )}\n          </div>\n          <div style={{ \n            fontSize: '12px', \n            color: '#aaa', \n            marginTop: '10px',\n            textAlign: 'center'\n          }}>\n            Topic: /camera/image_raw | 10 FPS\n          </div>\n        </div>\n\n        {/* Sensor Data Display */}\n        <div style={{\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        }}>\n          <h3 style={{ color: '#E91E63', marginTop: 0 }}>🌐 Sensor Data (/scan)</h3>\n          <div style={{\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444',\n            padding: '15px'\n          }}>\n            {sensorData.length > 0 ? (\n              <div>\n                <div style={{ marginBottom: '10px' }}>\n                  <strong style={{ color: '#E91E63' }}>Latest Scan:</strong>\n                </div>\n                <div style={{ fontSize: '14px', color: '#aaa', marginBottom: '8px' }}>\n                  Points: {sensorData[0]?.ranges?.length || 0}\n                </div>\n                <div style={{ fontSize: '14px', color: '#aaa', marginBottom: '8px' }}>\n                  Range: {sensorData[0]?.range_min?.toFixed(2) || 'N/A'}m - {sensorData[0]?.range_max?.toFixed(1) || 'N/A'}m\n                </div>\n                <div style={{ fontSize: '14px', color: '#aaa' }}>\n                  Angle: {((sensorData[0]?.angle_min || 0) * 180 / Math.PI).toFixed(1)}° - {((sensorData[0]?.angle_max || 0) * 180 / Math.PI).toFixed(1)}°\n                </div>\n              </div>\n            ) : (\n              <div style={{ color: '#666', textAlign: 'center', padding: '20px' }}>\n                Waiting for sensor data...\n              </div>\n            )}\n          </div>\n          <div style={{ fontSize: '12px', color: '#aaa', marginTop: '10px' }}>\n            Scans received: {sensorData.length}\n          </div>\n        </div>\n\n        {/* Map Status */}\n        <div style={{\n          backgroundColor: '#1a1a1a',\n          border: '1px solid #333',\n          borderRadius: '8px',\n          padding: '20px'\n        }}>\n          <h3 style={{ color: '#9C27B0', marginTop: 0 }}>🗺️ Map Status</h3>\n          <div style={{\n            backgroundColor: '#000',\n            borderRadius: '4px',\n            border: '1px solid #444',\n            padding: '15px'\n          }}>\n            <div style={{ marginBottom: '10px' }}>\n              <strong style={{ color: '#9C27B0' }}>Map Topic:</strong>\n            </div>\n            <div style={{ fontSize: '14px', color: '#aaa', marginBottom: '8px' }}>\n              Topic: /map\n            </div>\n            <div style={{ fontSize: '14px', color: '#aaa', marginBottom: '8px' }}>\n              Status: {robotData.connected ? 'Connected' : 'Disconnected'}\n            </div>\n            <div style={{ fontSize: '14px', color: '#aaa' }}>\n              Use Foxglove Studio below for map visualization\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Foxglove Studio Link */}\n      <FoxgloveStudioLink />\n\n      {/* Footer */}\n      <div style={{\n        textAlign: 'center',\n        padding: '20px',\n        borderTop: '1px solid #333',\n        color: '#666',\n        fontSize: '12px'\n      }}>\n        <div>🌐 TurtleBot3 Webviz-Style Dashboard via ROSBridge</div>\n        <div style={{ marginTop: '5px' }}>\n          Topics: /odom (plot), /tf (raw messages), /scan (3D), /camera/image_raw, /map | WebSocket: {ROS_WEBSOCKET_URL}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RobotDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAgB,OAAO;AAC1D,OAAOC,kBAAkB,MAAM,sBAAsB;;AAErD;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,iBAAiB,GAAG,yBAAyB;AACnD;AACA;;AAYA,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA,EAAAC,mBAAA,EAAAC,aAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,aAAA;EACrC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAY;IACpDkB,QAAQ,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAC9BC,WAAW,EAAE;MAAEH,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC;IACvCC,QAAQ,EAAE;MAAEC,MAAM,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAE,CAAC;IACnCC,OAAO,EAAE,CAAC;IAAE;IACZC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAS,CAAC,CAAC;EAC7D,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAQ,EAAE,CAAC;EACvD,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAQ,EAAE,CAAC;EACvD,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAgB,IAAI,CAAC;;EAEnE;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoC,KAAK,GAAGhC,iBAAiB;IAE/B,MAAMiC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChBC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1CzB,YAAY,CAAC0B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEf,SAAS,EAAE;MAAK,CAAC,CAAC,CAAC;;MAEpD;MACAU,EAAE,CAACM,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QACrBC,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;;MAEH;MACAX,EAAE,CAACM,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QACrBC,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;;MAEH;MACAX,EAAE,CAACM,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QACrBC,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,UAAU;QACjBC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;;MAEH;MACAX,EAAE,CAACM,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QACrBC,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;;MAEH;MACAX,EAAE,CAACM,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QACrBC,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;IACL,CAAC;IAEDX,EAAE,CAACY,SAAS,GAAIC,KAAK,IAAK;MACxB,IAAI;QACF,MAAMC,OAAO,GAAGP,IAAI,CAACQ,KAAK,CAACF,KAAK,CAACG,IAAI,CAAC;QAEtC,IAAIF,OAAO,CAACJ,KAAK,KAAK,OAAO,IAAII,OAAO,CAACG,GAAG,EAAE;UAC5C,MAAMC,IAAI,GAAGJ,OAAO,CAACG,GAAG,CAACC,IAAI,CAACA,IAAI;UAClC,MAAMC,KAAK,GAAGL,OAAO,CAACG,GAAG,CAACE,KAAK,CAACA,KAAK;UAErCxC,YAAY,CAAC0B,IAAI,KAAK;YACpB,GAAGA,IAAI;YACPzB,QAAQ,EAAE;cACRC,CAAC,EAAEqC,IAAI,CAACtC,QAAQ,CAACC,CAAC;cAClBC,CAAC,EAAEoC,IAAI,CAACtC,QAAQ,CAACE,CAAC;cAClBC,CAAC,EAAEmC,IAAI,CAACtC,QAAQ,CAACG;YACnB,CAAC;YACDC,WAAW,EAAE;cACXH,CAAC,EAAEqC,IAAI,CAAClC,WAAW,CAACH,CAAC;cACrBC,CAAC,EAAEoC,IAAI,CAAClC,WAAW,CAACF,CAAC;cACrBC,CAAC,EAAEmC,IAAI,CAAClC,WAAW,CAACD,CAAC;cACrBE,CAAC,EAAEiC,IAAI,CAAClC,WAAW,CAACC;YACtB,CAAC;YACDC,QAAQ,EAAE;cACRC,MAAM,EAAEiC,IAAI,CAACC,IAAI,CAACF,KAAK,CAAChC,MAAM,CAACN,CAAC,IAAI,CAAC,GAAGsC,KAAK,CAAChC,MAAM,CAACL,CAAC,IAAI,CAAC,CAAC;cAC5DM,OAAO,EAAE+B,KAAK,CAAC/B,OAAO,CAACL;YACzB;UACF,CAAC,CAAC,CAAC;;UAEH;UACAS,gBAAgB,CAACa,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QACpC;QAEA,IAAIS,OAAO,CAACJ,KAAK,KAAK,KAAK,IAAII,OAAO,CAACG,GAAG,EAAE;UAC1C;UACAvB,aAAa,CAACW,IAAI,IAAI,CAACS,OAAO,CAACG,GAAG,EAAE,GAAGZ,IAAI,CAACiB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D;QAEA,IAAIR,OAAO,CAACJ,KAAK,KAAK,OAAO,IAAII,OAAO,CAACG,GAAG,EAAE;UAC5C;UACArB,aAAa,CAACS,IAAI,IAAI,CAACS,OAAO,CAACG,GAAG,EAAE,GAAGZ,IAAI,CAACiB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D;QAEA,IAAIR,OAAO,CAACJ,KAAK,KAAK,gBAAgB,IAAII,OAAO,CAACG,GAAG,EAAE;UACrDtC,YAAY,CAAC0B,IAAI,KAAK;YACpB,GAAGA,IAAI;YACPhB,OAAO,EAAEyB,OAAO,CAACG,GAAG,CAACM,UAAU,GAAG;UACpC,CAAC,CAAC,CAAC;QACL;MAEF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdrB,OAAO,CAACqB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;IACF,CAAC;IAEDxB,EAAE,CAACyB,OAAO,GAAG,MAAM;MACjBtB,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/CzB,YAAY,CAAC0B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEf,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACvD,CAAC;IAEDU,EAAE,CAAC0B,OAAO,GAAIF,KAAK,IAAK;MACtBrB,OAAO,CAACqB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC7C,YAAY,CAAC0B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEf,SAAS,EAAE;MAAM,CAAC,CAAC,CAAC;IACvD,CAAC;IAED,OAAO,MAAM;MACXU,EAAE,CAAC2B,KAAK,CAAC,CAAC;IACZ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAhE,SAAS,CAAC,MAAM;IACd;IACA,MAAMoC,KAAK,GAAGhC,iBAAiB;IAC/B,MAAMiC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChB;MACAF,EAAE,CAACM,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QACrBC,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;IACL,CAAC;IAEDX,EAAE,CAACY,SAAS,GAAIC,KAAK,IAAK;MACxB,IAAI;QACF,MAAMC,OAAO,GAAGP,IAAI,CAACQ,KAAK,CAACF,KAAK,CAACG,IAAI,CAAC;QAEtC,IAAIF,OAAO,CAACJ,KAAK,KAAK,mBAAmB,IAAII,OAAO,CAACG,GAAG,EAAE;UACxD;UACA,MAAMW,QAAQ,GAAGd,OAAO,CAACG,GAAG;UAE5B,IAAIW,QAAQ,CAACC,QAAQ,KAAK,MAAM,IAAID,QAAQ,CAACC,QAAQ,KAAK,MAAM,EAAE;YAChE;YACA,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;YAC/CF,MAAM,CAACG,KAAK,GAAGL,QAAQ,CAACK,KAAK;YAC7BH,MAAM,CAACI,MAAM,GAAGN,QAAQ,CAACM,MAAM;YAC/B,MAAMC,GAAG,GAAGL,MAAM,CAACM,UAAU,CAAC,IAAI,CAAC;YAEnC,IAAID,GAAG,EAAE;cACP;cACA,MAAME,SAAS,GAAGF,GAAG,CAACG,eAAe,CAACV,QAAQ,CAACK,KAAK,EAAEL,QAAQ,CAACM,MAAM,CAAC;cACtE,MAAMlB,IAAI,GAAGuB,IAAI,CAACX,QAAQ,CAACZ,IAAI,CAAC;cAEhC,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxB,IAAI,CAACyB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;gBACvC,MAAME,UAAU,GAAIF,CAAC,GAAG,CAAC,GAAI,CAAC;gBAC9B,IAAIZ,QAAQ,CAACC,QAAQ,KAAK,MAAM,EAAE;kBAChCQ,SAAS,CAACrB,IAAI,CAAC0B,UAAU,CAAC,GAAG1B,IAAI,CAAC2B,UAAU,CAACH,CAAC,CAAC,CAAC,CAAK;kBACrDH,SAAS,CAACrB,IAAI,CAAC0B,UAAU,GAAG,CAAC,CAAC,GAAG1B,IAAI,CAAC2B,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;kBACzDH,SAAS,CAACrB,IAAI,CAAC0B,UAAU,GAAG,CAAC,CAAC,GAAG1B,IAAI,CAAC2B,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC3D,CAAC,MAAM;kBAAE;kBACPH,SAAS,CAACrB,IAAI,CAAC0B,UAAU,CAAC,GAAG1B,IAAI,CAAC2B,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,CAAK;kBACzDH,SAAS,CAACrB,IAAI,CAAC0B,UAAU,GAAG,CAAC,CAAC,GAAG1B,IAAI,CAAC2B,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;kBACzDH,SAAS,CAACrB,IAAI,CAAC0B,UAAU,GAAG,CAAC,CAAC,GAAG1B,IAAI,CAAC2B,UAAU,CAACH,CAAC,CAAC,CAAC,CAAK;gBAC3D;gBACAH,SAAS,CAACrB,IAAI,CAAC0B,UAAU,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;cACxC;cAEAP,GAAG,CAACS,YAAY,CAACP,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;cACjCvC,cAAc,CAACgC,MAAM,CAACe,SAAS,CAAC,CAAC,CAAC;YACpC;UACF;QACF;MACF,CAAC,CAAC,OAAOrB,KAAK,EAAE;QACdrB,OAAO,CAACqB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;IACF,CAAC;IAED,OAAO,MAAM;MACXxB,EAAE,CAAC2B,KAAK,CAAC,CAAC;IACZ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAIN,MAAMmB,eAAe,GAAIC,KAAa,IAAa;IACjD,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,SAAS;IAChC,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,SAAS;IAChC,OAAO,SAAS;EAClB,CAAC;EAED,oBACEjF,OAAA;IAAKkF,KAAK,EAAE;MACVC,UAAU,EAAE,SAAS;MACrBC,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,WAAW;MACvBC,OAAO,EAAE;IACX,CAAE;IAAAC,QAAA,gBAEAxF,OAAA;MAAKkF,KAAK,EAAE;QACVO,SAAS,EAAE,QAAQ;QACnBC,YAAY,EAAE,MAAM;QACpBC,YAAY,EAAE,gBAAgB;QAC9BC,aAAa,EAAE;MACjB,CAAE;MAAAJ,QAAA,gBACAxF,OAAA;QAAIkF,KAAK,EAAE;UAAEG,KAAK,EAAE,SAAS;UAAEQ,MAAM,EAAE;QAAa,CAAE;QAAAL,QAAA,EAAC;MAEvD;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLjG,OAAA;QAAKkF,KAAK,EAAE;UACVgB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,GAAG,EAAE,MAAM;UACXC,QAAQ,EAAE;QACZ,CAAE;QAAAd,QAAA,gBACAxF,OAAA;UAAKkF,KAAK,EAAE;YACVf,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdmC,YAAY,EAAE,KAAK;YACnBC,eAAe,EAAE5F,SAAS,CAACY,SAAS,GAAG,SAAS,GAAG;UACrD;QAAE;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACTjG,OAAA;UAAAwF,QAAA,EAAO5E,SAAS,CAACY,SAAS,GAAG,mBAAmB,GAAG;QAAc;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzEjG,OAAA;UAAMkF,KAAK,EAAE;YAAEuB,UAAU,EAAE,MAAM;YAAEH,QAAQ,EAAE,MAAM;YAAEjB,KAAK,EAAE;UAAO,CAAE;UAAAG,QAAA,EAAC;QAEtE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjG,OAAA;MAAKkF,KAAK,EAAE;QACVgB,OAAO,EAAE,MAAM;QACfQ,mBAAmB,EAAE,sCAAsC;QAC3DL,GAAG,EAAE,MAAM;QACXX,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBAGAxF,OAAA;QAAKkF,KAAK,EAAE;UACVsB,eAAe,EAAE,SAAS;UAC1BG,MAAM,EAAE,gBAAgB;UACxBJ,YAAY,EAAE,KAAK;UACnBhB,OAAO,EAAE;QACX,CAAE;QAAAC,QAAA,gBACAxF,OAAA;UAAIkF,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEuB,SAAS,EAAE;UAAE,CAAE;UAAApB,QAAA,EAAC;QAAwB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5EjG,OAAA;UAAKkF,KAAK,EAAE;YACVsB,eAAe,EAAE,MAAM;YACvBD,YAAY,EAAE,KAAK;YACnBI,MAAM,EAAE,gBAAgB;YACxBpB,OAAO,EAAE;UACX,CAAE;UAAAC,QAAA,gBACAxF,OAAA;YAAKkF,KAAK,EAAE;cAAEQ,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,eACnCxF,OAAA;cAAQkF,KAAK,EAAE;gBAAEG,KAAK,EAAE;cAAU,CAAE;cAAAG,QAAA,EAAC;YAAiB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eACNjG,OAAA;YAAKkF,KAAK,EAAE;cAAEoB,QAAQ,EAAE,MAAM;cAAEjB,KAAK,EAAE,MAAM;cAAEK,YAAY,EAAE;YAAM,CAAE;YAAAF,QAAA,GAAC,KACjE,EAAC5E,SAAS,CAACE,QAAQ,CAACC,CAAC,CAAC8F,OAAO,CAAC,CAAC,CAAC,EAAC,GACtC;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNjG,OAAA;YAAKkF,KAAK,EAAE;cAAEoB,QAAQ,EAAE,MAAM;cAAEjB,KAAK,EAAE,MAAM;cAAEK,YAAY,EAAE;YAAM,CAAE;YAAAF,QAAA,GAAC,KACjE,EAAC5E,SAAS,CAACE,QAAQ,CAACE,CAAC,CAAC6F,OAAO,CAAC,CAAC,CAAC,EAAC,GACtC;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNjG,OAAA;YAAKkF,KAAK,EAAE;cAAEoB,QAAQ,EAAE,MAAM;cAAEjB,KAAK,EAAE;YAAO,CAAE;YAAAG,QAAA,GAAC,KAC5C,EAAC5E,SAAS,CAACE,QAAQ,CAACG,CAAC,CAAC4F,OAAO,CAAC,CAAC,CAAC,EAAC,GACtC;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjG,OAAA;UAAKkF,KAAK,EAAE;YAAEoB,QAAQ,EAAE,MAAM;YAAEjB,KAAK,EAAE,MAAM;YAAEuB,SAAS,EAAE;UAAO,CAAE;UAAApB,QAAA,GAAC,wBAC5C,EAAC/D,aAAa;QAAA;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjG,OAAA;QAAKkF,KAAK,EAAE;UACVsB,eAAe,EAAE,SAAS;UAC1BG,MAAM,EAAE,gBAAgB;UACxBJ,YAAY,EAAE,KAAK;UACnBhB,OAAO,EAAE;QACX,CAAE;QAAAC,QAAA,gBACAxF,OAAA;UAAIkF,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEuB,SAAS,EAAE;UAAE,CAAE;UAAApB,QAAA,EAAC;QAAqB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzEjG,OAAA;UAAKkF,KAAK,EAAE;YACVd,MAAM,EAAE,OAAO;YACfoC,eAAe,EAAE,MAAM;YACvBD,YAAY,EAAE,KAAK;YACnBI,MAAM,EAAE,gBAAgB;YACxBpB,OAAO,EAAE,MAAM;YACfuB,QAAQ,EAAE,MAAM;YAChBxB,UAAU,EAAE,WAAW;YACvBgB,QAAQ,EAAE;UACZ,CAAE;UAAAd,QAAA,EACC7D,UAAU,CAACgD,MAAM,GAAG,CAAC,GACpBhD,UAAU,CAACoF,GAAG,CAAC,CAAC5D,GAAG,EAAE6D,KAAK;YAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;YAAA,oBACxB1H,OAAA;cAAiBkF,KAAK,EAAE;gBACtBQ,YAAY,EAAE,KAAK;gBACnBL,KAAK,EAAE2B,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,MAAM;gBACvCrB,YAAY,EAAE,gBAAgB;gBAC9BC,aAAa,EAAE;cACjB,CAAE;cAAAJ,QAAA,gBACAxF,OAAA;gBAAKkF,KAAK,EAAE;kBAAEG,KAAK,EAAE;gBAAU,CAAE;gBAAAG,QAAA,GAAC,aACrB,EAACwB,KAAK,GAAG,CAAC,EAAC,GACxB;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNjG,OAAA;gBAAAwF,QAAA,GAAK,UACK,EAAC,EAAAyB,eAAA,GAAA9D,GAAG,CAACwE,UAAU,cAAAV,eAAA,wBAAAC,gBAAA,GAAdD,eAAA,CAAiB,CAAC,CAAC,cAAAC,gBAAA,wBAAAC,qBAAA,GAAnBD,gBAAA,CAAqBU,MAAM,cAAAT,qBAAA,uBAA3BA,qBAAA,CAA6BU,QAAQ,KAAI,KAAK,EAAC,UAAG,EAAC,EAAAT,gBAAA,GAAAjE,GAAG,CAACwE,UAAU,cAAAP,gBAAA,wBAAAC,iBAAA,GAAdD,gBAAA,CAAiB,CAAC,CAAC,cAAAC,iBAAA,uBAAnBA,iBAAA,CAAqBS,cAAc,KAAI,KAAK;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrG,CAAC,EACL,EAAAqB,gBAAA,GAAAnE,GAAG,CAACwE,UAAU,cAAAL,gBAAA,wBAAAC,iBAAA,GAAdD,gBAAA,CAAiB,CAAC,CAAC,cAAAC,iBAAA,uBAAnBA,iBAAA,CAAqBQ,SAAS,kBAC7B/H,OAAA;gBAAAwF,QAAA,GAAK,gBACW,GAAAgC,qBAAA,GAACrE,GAAG,CAACwE,UAAU,CAAC,CAAC,CAAC,CAACI,SAAS,CAACC,WAAW,CAACjH,CAAC,cAAAyG,qBAAA,uBAAzCA,qBAAA,CAA2CX,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,GAAAY,sBAAA,GAACtE,GAAG,CAACwE,UAAU,CAAC,CAAC,CAAC,CAACI,SAAS,CAACC,WAAW,CAAChH,CAAC,cAAAyG,sBAAA,uBAAzCA,sBAAA,CAA2CZ,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,GAAAa,sBAAA,GAACvE,GAAG,CAACwE,UAAU,CAAC,CAAC,CAAC,CAACI,SAAS,CAACC,WAAW,CAAC/G,CAAC,cAAAyG,sBAAA,uBAAzCA,sBAAA,CAA2Cb,OAAO,CAAC,CAAC,CAAC,EAAC,GACzL;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA,GAhBOe,KAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBV,CAAC;UAAA,CACP,CAAC,gBAEFjG,OAAA;YAAKkF,KAAK,EAAE;cAAEG,KAAK,EAAE,MAAM;cAAEI,SAAS,EAAE,QAAQ;cAAEmB,SAAS,EAAE;YAAO,CAAE;YAAApB,QAAA,EAAC;UAEvE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNjG,OAAA;UAAKkF,KAAK,EAAE;YAAEoB,QAAQ,EAAE,MAAM;YAAEjB,KAAK,EAAE,MAAM;YAAEuB,SAAS,EAAE;UAAO,CAAE;UAAApB,QAAA,GAAC,qBAC/C,EAAC7D,UAAU,CAACgD,MAAM,EAAC,eACxC;QAAA;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjG,OAAA;QAAKkF,KAAK,EAAE;UACVsB,eAAe,EAAE,SAAS;UAC1BG,MAAM,EAAE,gBAAgB;UACxBJ,YAAY,EAAE,KAAK;UACnBhB,OAAO,EAAE;QACX,CAAE;QAAAC,QAAA,gBACAxF,OAAA;UAAIkF,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEuB,SAAS,EAAE;UAAE,CAAE;UAAApB,QAAA,EAAC;QAAe;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEnEjG,OAAA;UAAKkF,KAAK,EAAE;YAAEQ,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBACnCxF,OAAA;YAAKkF,KAAK,EAAE;cAAEgB,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE,eAAe;cAAEV,YAAY,EAAE;YAAM,CAAE;YAAAF,QAAA,eACpFxF,OAAA;cAAAwF,QAAA,EAAM;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACNjG,OAAA;YAAKkF,KAAK,EAAE;cAAEoB,QAAQ,EAAE,MAAM;cAAEjB,KAAK,EAAE;YAAO,CAAE;YAAAG,QAAA,GAAC,KAC5C,EAAC5E,SAAS,CAACE,QAAQ,CAACC,CAAC,CAAC8F,OAAO,CAAC,CAAC,CAAC,EAAC,SACjC,EAACjG,SAAS,CAACE,QAAQ,CAACE,CAAC,CAAC6F,OAAO,CAAC,CAAC,CAAC,EAAC,SACjC,EAACjG,SAAS,CAACE,QAAQ,CAACG,CAAC,CAAC4F,OAAO,CAAC,CAAC,CAAC,EAAC,GACtC;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjG,OAAA;UAAKkF,KAAK,EAAE;YAAEQ,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBACnCxF,OAAA;YAAKkF,KAAK,EAAE;cAAEgB,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE,eAAe;cAAEV,YAAY,EAAE;YAAM,CAAE;YAAAF,QAAA,eACpFxF,OAAA;cAAAwF,QAAA,EAAM;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACNjG,OAAA;YAAKkF,KAAK,EAAE;cAAEoB,QAAQ,EAAE,MAAM;cAAEjB,KAAK,EAAE;YAAO,CAAE;YAAAG,QAAA,GAAC,UACvC,EAAC5E,SAAS,CAACQ,QAAQ,CAACC,MAAM,CAACwF,OAAO,CAAC,CAAC,CAAC,EAAC,iBACrC,EAACjG,SAAS,CAACQ,QAAQ,CAACE,OAAO,CAACuF,OAAO,CAAC,CAAC,CAAC,EAAC,OAClD;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjG,OAAA;UAAKkF,KAAK,EAAE;YAAEQ,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBACnCxF,OAAA;YAAKkF,KAAK,EAAE;cAAEgB,OAAO,EAAE,MAAM;cAAEE,cAAc,EAAE,eAAe;cAAEV,YAAY,EAAE;YAAM,CAAE;YAAAF,QAAA,gBACpFxF,OAAA;cAAAwF,QAAA,EAAM;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrBjG,OAAA;cAAMkF,KAAK,EAAE;gBAAEG,KAAK,EAAEL,eAAe,CAACpE,SAAS,CAACW,OAAO;cAAE,CAAE;cAAAiE,QAAA,GACxD5E,SAAS,CAACW,OAAO,CAACsF,OAAO,CAAC,CAAC,CAAC,EAAC,GAChC;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNjG,OAAA;YAAKkF,KAAK,EAAE;cACVf,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,KAAK;cACboC,eAAe,EAAE,MAAM;cACvBD,YAAY,EAAE,KAAK;cACnBO,QAAQ,EAAE;YACZ,CAAE;YAAAtB,QAAA,eACAxF,OAAA;cAAKkF,KAAK,EAAE;gBACVf,KAAK,EAAE,GAAGvD,SAAS,CAACW,OAAO,GAAG;gBAC9B6C,MAAM,EAAE,MAAM;gBACdoC,eAAe,EAAExB,eAAe,CAACpE,SAAS,CAACW,OAAO,CAAC;gBACnD0G,UAAU,EAAE;cACd;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjG,OAAA;QAAKkF,KAAK,EAAE;UACVsB,eAAe,EAAE,SAAS;UAC1BG,MAAM,EAAE,gBAAgB;UACxBJ,YAAY,EAAE,KAAK;UACnBhB,OAAO,EAAE;QACX,CAAE;QAAAC,QAAA,gBACAxF,OAAA;UAAIkF,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEuB,SAAS,EAAE;UAAE,CAAE;UAAApB,QAAA,EAAC;QAAc;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClEjG,OAAA;UAAKkF,KAAK,EAAE;YACVgB,OAAO,EAAE,MAAM;YACfE,cAAc,EAAE,QAAQ;YACxBD,UAAU,EAAE,QAAQ;YACpBf,SAAS,EAAE,OAAO;YAClBoB,eAAe,EAAE,MAAM;YACvBD,YAAY,EAAE,KAAK;YACnBI,MAAM,EAAE;UACV,CAAE;UAAAnB,QAAA,EACCzD,WAAW,gBACV/B,OAAA;YACEkI,GAAG,EAAEnG,WAAY;YACjBoG,GAAG,EAAC,cAAc;YAClBjD,KAAK,EAAE;cACLkD,QAAQ,EAAE,MAAM;cAChBC,SAAS,EAAE,OAAO;cAClB9B,YAAY,EAAE;YAChB;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEFjG,OAAA;YAAKkF,KAAK,EAAE;cAAEG,KAAK,EAAE,MAAM;cAAEI,SAAS,EAAE;YAAS,CAAE;YAAAD,QAAA,gBACjDxF,OAAA;cAAKkF,KAAK,EAAE;gBAAEoB,QAAQ,EAAE,MAAM;gBAAEZ,YAAY,EAAE;cAAO,CAAE;cAAAF,QAAA,EAAC;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChEjG,OAAA;cAAAwF,QAAA,EAAK;YAAc;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNjG,OAAA;UAAKkF,KAAK,EAAE;YACVoB,QAAQ,EAAE,MAAM;YAChBjB,KAAK,EAAE,MAAM;YACbuB,SAAS,EAAE,MAAM;YACjBnB,SAAS,EAAE;UACb,CAAE;UAAAD,QAAA,EAAC;QAEH;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjG,OAAA;QAAKkF,KAAK,EAAE;UACVsB,eAAe,EAAE,SAAS;UAC1BG,MAAM,EAAE,gBAAgB;UACxBJ,YAAY,EAAE,KAAK;UACnBhB,OAAO,EAAE;QACX,CAAE;QAAAC,QAAA,gBACAxF,OAAA;UAAIkF,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEuB,SAAS,EAAE;UAAE,CAAE;UAAApB,QAAA,EAAC;QAAsB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1EjG,OAAA;UAAKkF,KAAK,EAAE;YACVsB,eAAe,EAAE,MAAM;YACvBD,YAAY,EAAE,KAAK;YACnBI,MAAM,EAAE,gBAAgB;YACxBpB,OAAO,EAAE;UACX,CAAE;UAAAC,QAAA,EACC3D,UAAU,CAAC8C,MAAM,GAAG,CAAC,gBACpB3E,OAAA;YAAAwF,QAAA,gBACExF,OAAA;cAAKkF,KAAK,EAAE;gBAAEQ,YAAY,EAAE;cAAO,CAAE;cAAAF,QAAA,eACnCxF,OAAA;gBAAQkF,KAAK,EAAE;kBAAEG,KAAK,EAAE;gBAAU,CAAE;gBAAAG,QAAA,EAAC;cAAY;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACNjG,OAAA;cAAKkF,KAAK,EAAE;gBAAEoB,QAAQ,EAAE,MAAM;gBAAEjB,KAAK,EAAE,MAAM;gBAAEK,YAAY,EAAE;cAAM,CAAE;cAAAF,QAAA,GAAC,UAC5D,EAAC,EAAApF,YAAA,GAAAyB,UAAU,CAAC,CAAC,CAAC,cAAAzB,YAAA,wBAAAC,mBAAA,GAAbD,YAAA,CAAekI,MAAM,cAAAjI,mBAAA,uBAArBA,mBAAA,CAAuBsE,MAAM,KAAI,CAAC;YAAA;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACNjG,OAAA;cAAKkF,KAAK,EAAE;gBAAEoB,QAAQ,EAAE,MAAM;gBAAEjB,KAAK,EAAE,MAAM;gBAAEK,YAAY,EAAE;cAAM,CAAE;cAAAF,QAAA,GAAC,SAC7D,EAAC,EAAAlF,aAAA,GAAAuB,UAAU,CAAC,CAAC,CAAC,cAAAvB,aAAA,wBAAAC,qBAAA,GAAbD,aAAA,CAAeiI,SAAS,cAAAhI,qBAAA,uBAAxBA,qBAAA,CAA0BsG,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,EAAC,MAAI,EAAC,EAAArG,aAAA,GAAAqB,UAAU,CAAC,CAAC,CAAC,cAAArB,aAAA,wBAAAC,qBAAA,GAAbD,aAAA,CAAegI,SAAS,cAAA/H,qBAAA,uBAAxBA,qBAAA,CAA0BoG,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,EAAC,GAC3G;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNjG,OAAA;cAAKkF,KAAK,EAAE;gBAAEoB,QAAQ,EAAE,MAAM;gBAAEjB,KAAK,EAAE;cAAO,CAAE;cAAAG,QAAA,GAAC,SACxC,EAAC,CAAC,CAAC,EAAA9E,aAAA,GAAAmB,UAAU,CAAC,CAAC,CAAC,cAAAnB,aAAA,uBAAbA,aAAA,CAAe+H,SAAS,KAAI,CAAC,IAAI,GAAG,GAAGnF,IAAI,CAACoF,EAAE,EAAE7B,OAAO,CAAC,CAAC,CAAC,EAAC,SAAI,EAAC,CAAC,CAAC,EAAAlG,aAAA,GAAAkB,UAAU,CAAC,CAAC,CAAC,cAAAlB,aAAA,uBAAbA,aAAA,CAAegI,SAAS,KAAI,CAAC,IAAI,GAAG,GAAGrF,IAAI,CAACoF,EAAE,EAAE7B,OAAO,CAAC,CAAC,CAAC,EAAC,MACzI;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENjG,OAAA;YAAKkF,KAAK,EAAE;cAAEG,KAAK,EAAE,MAAM;cAAEI,SAAS,EAAE,QAAQ;cAAEF,OAAO,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAErE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNjG,OAAA;UAAKkF,KAAK,EAAE;YAAEoB,QAAQ,EAAE,MAAM;YAAEjB,KAAK,EAAE,MAAM;YAAEuB,SAAS,EAAE;UAAO,CAAE;UAAApB,QAAA,GAAC,kBAClD,EAAC3D,UAAU,CAAC8C,MAAM;QAAA;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjG,OAAA;QAAKkF,KAAK,EAAE;UACVsB,eAAe,EAAE,SAAS;UAC1BG,MAAM,EAAE,gBAAgB;UACxBJ,YAAY,EAAE,KAAK;UACnBhB,OAAO,EAAE;QACX,CAAE;QAAAC,QAAA,gBACAxF,OAAA;UAAIkF,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEuB,SAAS,EAAE;UAAE,CAAE;UAAApB,QAAA,EAAC;QAAc;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClEjG,OAAA;UAAKkF,KAAK,EAAE;YACVsB,eAAe,EAAE,MAAM;YACvBD,YAAY,EAAE,KAAK;YACnBI,MAAM,EAAE,gBAAgB;YACxBpB,OAAO,EAAE;UACX,CAAE;UAAAC,QAAA,gBACAxF,OAAA;YAAKkF,KAAK,EAAE;cAAEQ,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,eACnCxF,OAAA;cAAQkF,KAAK,EAAE;gBAAEG,KAAK,EAAE;cAAU,CAAE;cAAAG,QAAA,EAAC;YAAU;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACNjG,OAAA;YAAKkF,KAAK,EAAE;cAAEoB,QAAQ,EAAE,MAAM;cAAEjB,KAAK,EAAE,MAAM;cAAEK,YAAY,EAAE;YAAM,CAAE;YAAAF,QAAA,EAAC;UAEtE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNjG,OAAA;YAAKkF,KAAK,EAAE;cAAEoB,QAAQ,EAAE,MAAM;cAAEjB,KAAK,EAAE,MAAM;cAAEK,YAAY,EAAE;YAAM,CAAE;YAAAF,QAAA,GAAC,UAC5D,EAAC5E,SAAS,CAACY,SAAS,GAAG,WAAW,GAAG,cAAc;UAAA;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACNjG,OAAA;YAAKkF,KAAK,EAAE;cAAEoB,QAAQ,EAAE,MAAM;cAAEjB,KAAK,EAAE;YAAO,CAAE;YAAAG,QAAA,EAAC;UAEjD;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjG,OAAA,CAACF,kBAAkB;MAAAgG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGtBjG,OAAA;MAAKkF,KAAK,EAAE;QACVO,SAAS,EAAE,QAAQ;QACnBF,OAAO,EAAE,MAAM;QACfqD,SAAS,EAAE,gBAAgB;QAC3BvD,KAAK,EAAE,MAAM;QACbiB,QAAQ,EAAE;MACZ,CAAE;MAAAd,QAAA,gBACAxF,OAAA;QAAAwF,QAAA,EAAK;MAAkD;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC7DjG,OAAA;QAAKkF,KAAK,EAAE;UAAE0B,SAAS,EAAE;QAAM,CAAE;QAAApB,QAAA,GAAC,6FAC2D,EAACvF,iBAAiB;MAAA;QAAA6F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9F,EAAA,CAlgBID,cAAwB;AAAA2I,EAAA,GAAxB3I,cAAwB;AAogB9B,eAAeA,cAAc;AAAC,IAAA2I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}