{"ast": null, "code": "var _jsxFileName = \"F:\\\\foxglove\\\\foxglove-panels\\\\src\\\\components\\\\FoxgloveStudioLink.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FoxgloveStudioLink = () => {\n  // Generate Foxglove Studio URL with your ROS2 connection\n  const generateFoxgloveUrl = () => {\n    const baseUrl = 'https://studio.foxglove.dev/';\n\n    // Create a layout configuration for your specific needs\n    const layoutConfig = {\n      \"configById\": {\n        \"3D!4co6n9d\": {\n          \"layers\": {\n            \"grid\": {\n              \"layerId\": \"grid\"\n            },\n            \"tf\": {\n              \"layerId\": \"tf\"\n            }\n          },\n          \"cameraState\": {\n            \"perspective\": true,\n            \"distance\": 20,\n            \"phi\": 60,\n            \"thetaOffset\": 45,\n            \"targetOffset\": [0, 0, 0],\n            \"target\": [0, 0, 0],\n            \"targetOrientation\": [0, 0, 0, 1]\n          },\n          \"followMode\": \"follow-pose\",\n          \"scene\": {\n            \"transforms\": {\n              \"showLabel\": false,\n              \"editable\": true\n            }\n          }\n        },\n        \"Plot!3xbqnp\": {\n          \"paths\": [{\n            \"timestampMethod\": \"receiveTime\",\n            \"value\": \"/odom.pose.pose.position.x\",\n            \"enabled\": true,\n            \"label\": \"Position X\"\n          }, {\n            \"timestampMethod\": \"receiveTime\",\n            \"value\": \"/odom.pose.pose.position.y\",\n            \"enabled\": true,\n            \"label\": \"Position Y\"\n          }],\n          \"showXAxisLabels\": true,\n          \"showYAxisLabels\": true,\n          \"showLegend\": true,\n          \"legendDisplay\": \"floating\",\n          \"showPlotValuesInLegend\": false,\n          \"isSynced\": true,\n          \"xAxisVal\": \"timestamp\",\n          \"sidebarDimension\": 240\n        },\n        \"RawMessages!2w7s9m\": {\n          \"diffEnabled\": false,\n          \"diffMethod\": \"custom\",\n          \"diffTopicPath\": \"\",\n          \"showFullMessageForDiff\": false,\n          \"topicPath\": \"/tf\"\n        },\n        \"Image!1a2b3c\": {\n          \"cameraTopic\": \"/camera/image_raw\",\n          \"enabledMarkerTopics\": [],\n          \"scale\": 0.2,\n          \"pan\": {\n            \"x\": 0,\n            \"y\": 0\n          },\n          \"rotation\": 0,\n          \"flipHorizontal\": false,\n          \"flipVertical\": false,\n          \"minValue\": 0,\n          \"maxValue\": 10000\n        }\n      },\n      \"globalVariables\": {},\n      \"userNodes\": {},\n      \"linkedGlobalVariables\": [],\n      \"playbackConfig\": {\n        \"speed\": 1,\n        \"messageOrder\": \"receiveTime\"\n      },\n      \"layout\": {\n        \"direction\": \"row\",\n        \"first\": {\n          \"direction\": \"column\",\n          \"first\": \"3D!4co6n9d\",\n          \"second\": {\n            \"direction\": \"row\",\n            \"first\": \"Plot!3xbqnp\",\n            \"second\": \"RawMessages!2w7s9m\",\n            \"splitPercentage\": 50\n          },\n          \"splitPercentage\": 60\n        },\n        \"second\": \"Image!1a2b3c\",\n        \"splitPercentage\": 70\n      }\n    };\n\n    // Encode the layout as URL parameter\n    const layoutParam = encodeURIComponent(JSON.stringify(layoutConfig));\n\n    // Your ROS2 connection parameters\n    const connectionParams = new URLSearchParams({\n      'ds': 'rosbridge-websocket',\n      'ds.url': 'ws://192.168.1.192:9090',\n      'layoutId': 'custom',\n      'layout': layoutParam\n    });\n    return `${baseUrl}?${connectionParams.toString()}`;\n  };\n  const openFoxgloveStudio = () => {\n    const url = generateFoxgloveUrl();\n    window.open(url, '_blank');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      backgroundColor: '#1a1a1a',\n      border: '2px solid #4CAF50',\n      borderRadius: '12px',\n      padding: '24px',\n      margin: '20px 0',\n      textAlign: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      style: {\n        color: '#4CAF50',\n        marginTop: 0\n      },\n      children: \"\\uD83E\\uDD8A Open in Foxglove Studio\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        color: '#aaa',\n        marginBottom: '20px'\n      },\n      children: \"Launch Foxglove Studio with pre-configured layout for your TurtleBot3:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#000',\n        padding: '15px',\n        borderRadius: '8px',\n        marginBottom: '20px',\n        fontFamily: 'monospace',\n        fontSize: '12px',\n        color: '#00ff00'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u2705 3D Visualization Panel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u2705 Position Plot (/odom \\u2192 position.x, position.y)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u2705 Raw Messages Panel (/tf)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u2705 Camera Feed (/camera/image_raw)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u2705 Pre-connected to: ws://192.168.1.192:9090\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: openFoxgloveStudio,\n      style: {\n        backgroundColor: '#4CAF50',\n        color: 'white',\n        border: 'none',\n        padding: '12px 24px',\n        borderRadius: '6px',\n        fontSize: '16px',\n        cursor: 'pointer',\n        fontWeight: 'bold',\n        transition: 'background-color 0.2s'\n      },\n      onMouseOver: e => e.currentTarget.style.backgroundColor = '#45a049',\n      onMouseOut: e => e.currentTarget.style.backgroundColor = '#4CAF50',\n      children: \"\\uD83D\\uDE80 Launch Foxglove Studio\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '12px',\n        color: '#666',\n        marginTop: '15px'\n      },\n      children: \"Opens in new tab with your ROS2 data source pre-configured\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 5\n  }, this);\n};\n_c = FoxgloveStudioLink;\nexport default FoxgloveStudioLink;\nvar _c;\n$RefreshReg$(_c, \"FoxgloveStudioLink\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "FoxgloveStudioLink", "generateFoxgloveUrl", "baseUrl", "layoutConfig", "layoutPara<PERSON>", "encodeURIComponent", "JSON", "stringify", "connectionParams", "URLSearchParams", "toString", "openFoxgloveStudio", "url", "window", "open", "style", "backgroundColor", "border", "borderRadius", "padding", "margin", "textAlign", "children", "color", "marginTop", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "fontFamily", "fontSize", "onClick", "cursor", "fontWeight", "transition", "onMouseOver", "e", "currentTarget", "onMouseOut", "_c", "$RefreshReg$"], "sources": ["F:/foxglove/foxglove-panels/src/components/FoxgloveStudioLink.tsx"], "sourcesContent": ["import React from 'react';\n\nconst FoxgloveStudioLink: React.FC = () => {\n  // Generate Foxglove Studio URL with your ROS2 connection\n  const generateFoxgloveUrl = () => {\n    const baseUrl = 'https://studio.foxglove.dev/';\n    \n    // Create a layout configuration for your specific needs\n    const layoutConfig = {\n      \"configById\": {\n        \"3D!4co6n9d\": {\n          \"layers\": {\n            \"grid\": { \"layerId\": \"grid\" },\n            \"tf\": { \"layerId\": \"tf\" }\n          },\n          \"cameraState\": {\n            \"perspective\": true,\n            \"distance\": 20,\n            \"phi\": 60,\n            \"thetaOffset\": 45,\n            \"targetOffset\": [0, 0, 0],\n            \"target\": [0, 0, 0],\n            \"targetOrientation\": [0, 0, 0, 1]\n          },\n          \"followMode\": \"follow-pose\",\n          \"scene\": {\n            \"transforms\": {\n              \"showLabel\": false,\n              \"editable\": true\n            }\n          }\n        },\n        \"Plot!3xbqnp\": {\n          \"paths\": [\n            {\n              \"timestampMethod\": \"receiveTime\",\n              \"value\": \"/odom.pose.pose.position.x\",\n              \"enabled\": true,\n              \"label\": \"Position X\"\n            },\n            {\n              \"timestampMethod\": \"receiveTime\", \n              \"value\": \"/odom.pose.pose.position.y\",\n              \"enabled\": true,\n              \"label\": \"Position Y\"\n            }\n          ],\n          \"showXAxisLabels\": true,\n          \"showYAxisLabels\": true,\n          \"showLegend\": true,\n          \"legendDisplay\": \"floating\",\n          \"showPlotValuesInLegend\": false,\n          \"isSynced\": true,\n          \"xAxisVal\": \"timestamp\",\n          \"sidebarDimension\": 240\n        },\n        \"RawMessages!2w7s9m\": {\n          \"diffEnabled\": false,\n          \"diffMethod\": \"custom\",\n          \"diffTopicPath\": \"\",\n          \"showFullMessageForDiff\": false,\n          \"topicPath\": \"/tf\"\n        },\n        \"Image!1a2b3c\": {\n          \"cameraTopic\": \"/camera/image_raw\",\n          \"enabledMarkerTopics\": [],\n          \"scale\": 0.2,\n          \"pan\": { \"x\": 0, \"y\": 0 },\n          \"rotation\": 0,\n          \"flipHorizontal\": false,\n          \"flipVertical\": false,\n          \"minValue\": 0,\n          \"maxValue\": 10000\n        }\n      },\n      \"globalVariables\": {},\n      \"userNodes\": {},\n      \"linkedGlobalVariables\": [],\n      \"playbackConfig\": {\n        \"speed\": 1,\n        \"messageOrder\": \"receiveTime\"\n      },\n      \"layout\": {\n        \"direction\": \"row\",\n        \"first\": {\n          \"direction\": \"column\",\n          \"first\": \"3D!4co6n9d\",\n          \"second\": {\n            \"direction\": \"row\",\n            \"first\": \"Plot!3xbqnp\",\n            \"second\": \"RawMessages!2w7s9m\",\n            \"splitPercentage\": 50\n          },\n          \"splitPercentage\": 60\n        },\n        \"second\": \"Image!1a2b3c\",\n        \"splitPercentage\": 70\n      }\n    };\n\n    // Encode the layout as URL parameter\n    const layoutParam = encodeURIComponent(JSON.stringify(layoutConfig));\n    \n    // Your ROS2 connection parameters\n    const connectionParams = new URLSearchParams({\n      'ds': 'rosbridge-websocket',\n      'ds.url': 'ws://192.168.1.192:9090',\n      'layoutId': 'custom',\n      'layout': layoutParam\n    });\n\n    return `${baseUrl}?${connectionParams.toString()}`;\n  };\n\n  const openFoxgloveStudio = () => {\n    const url = generateFoxgloveUrl();\n    window.open(url, '_blank');\n  };\n\n  return (\n    <div style={{\n      backgroundColor: '#1a1a1a',\n      border: '2px solid #4CAF50',\n      borderRadius: '12px',\n      padding: '24px',\n      margin: '20px 0',\n      textAlign: 'center'\n    }}>\n      <h2 style={{ color: '#4CAF50', marginTop: 0 }}>\n        🦊 Open in Foxglove Studio\n      </h2>\n      \n      <p style={{ color: '#aaa', marginBottom: '20px' }}>\n        Launch Foxglove Studio with pre-configured layout for your TurtleBot3:\n      </p>\n      \n      <div style={{ \n        backgroundColor: '#000', \n        padding: '15px', \n        borderRadius: '8px',\n        marginBottom: '20px',\n        fontFamily: 'monospace',\n        fontSize: '12px',\n        color: '#00ff00'\n      }}>\n        <div>✅ 3D Visualization Panel</div>\n        <div>✅ Position Plot (/odom → position.x, position.y)</div>\n        <div>✅ Raw Messages Panel (/tf)</div>\n        <div>✅ Camera Feed (/camera/image_raw)</div>\n        <div>✅ Pre-connected to: ws://192.168.1.192:9090</div>\n      </div>\n\n      <button\n        onClick={openFoxgloveStudio}\n        style={{\n          backgroundColor: '#4CAF50',\n          color: 'white',\n          border: 'none',\n          padding: '12px 24px',\n          borderRadius: '6px',\n          fontSize: '16px',\n          cursor: 'pointer',\n          fontWeight: 'bold',\n          transition: 'background-color 0.2s'\n        }}\n        onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#45a049'}\n        onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#4CAF50'}\n      >\n        🚀 Launch Foxglove Studio\n      </button>\n      \n      <div style={{ \n        fontSize: '12px', \n        color: '#666', \n        marginTop: '15px' \n      }}>\n        Opens in new tab with your ROS2 data source pre-configured\n      </div>\n    </div>\n  );\n};\n\nexport default FoxgloveStudioLink;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EACzC;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,OAAO,GAAG,8BAA8B;;IAE9C;IACA,MAAMC,YAAY,GAAG;MACnB,YAAY,EAAE;QACZ,YAAY,EAAE;UACZ,QAAQ,EAAE;YACR,MAAM,EAAE;cAAE,SAAS,EAAE;YAAO,CAAC;YAC7B,IAAI,EAAE;cAAE,SAAS,EAAE;YAAK;UAC1B,CAAC;UACD,aAAa,EAAE;YACb,aAAa,EAAE,IAAI;YACnB,UAAU,EAAE,EAAE;YACd,KAAK,EAAE,EAAE;YACT,aAAa,EAAE,EAAE;YACjB,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACzB,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACnB,mBAAmB,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;UAClC,CAAC;UACD,YAAY,EAAE,aAAa;UAC3B,OAAO,EAAE;YACP,YAAY,EAAE;cACZ,WAAW,EAAE,KAAK;cAClB,UAAU,EAAE;YACd;UACF;QACF,CAAC;QACD,aAAa,EAAE;UACb,OAAO,EAAE,CACP;YACE,iBAAiB,EAAE,aAAa;YAChC,OAAO,EAAE,4BAA4B;YACrC,SAAS,EAAE,IAAI;YACf,OAAO,EAAE;UACX,CAAC,EACD;YACE,iBAAiB,EAAE,aAAa;YAChC,OAAO,EAAE,4BAA4B;YACrC,SAAS,EAAE,IAAI;YACf,OAAO,EAAE;UACX,CAAC,CACF;UACD,iBAAiB,EAAE,IAAI;UACvB,iBAAiB,EAAE,IAAI;UACvB,YAAY,EAAE,IAAI;UAClB,eAAe,EAAE,UAAU;UAC3B,wBAAwB,EAAE,KAAK;UAC/B,UAAU,EAAE,IAAI;UAChB,UAAU,EAAE,WAAW;UACvB,kBAAkB,EAAE;QACtB,CAAC;QACD,oBAAoB,EAAE;UACpB,aAAa,EAAE,KAAK;UACpB,YAAY,EAAE,QAAQ;UACtB,eAAe,EAAE,EAAE;UACnB,wBAAwB,EAAE,KAAK;UAC/B,WAAW,EAAE;QACf,CAAC;QACD,cAAc,EAAE;UACd,aAAa,EAAE,mBAAmB;UAClC,qBAAqB,EAAE,EAAE;UACzB,OAAO,EAAE,GAAG;UACZ,KAAK,EAAE;YAAE,GAAG,EAAE,CAAC;YAAE,GAAG,EAAE;UAAE,CAAC;UACzB,UAAU,EAAE,CAAC;UACb,gBAAgB,EAAE,KAAK;UACvB,cAAc,EAAE,KAAK;UACrB,UAAU,EAAE,CAAC;UACb,UAAU,EAAE;QACd;MACF,CAAC;MACD,iBAAiB,EAAE,CAAC,CAAC;MACrB,WAAW,EAAE,CAAC,CAAC;MACf,uBAAuB,EAAE,EAAE;MAC3B,gBAAgB,EAAE;QAChB,OAAO,EAAE,CAAC;QACV,cAAc,EAAE;MAClB,CAAC;MACD,QAAQ,EAAE;QACR,WAAW,EAAE,KAAK;QAClB,OAAO,EAAE;UACP,WAAW,EAAE,QAAQ;UACrB,OAAO,EAAE,YAAY;UACrB,QAAQ,EAAE;YACR,WAAW,EAAE,KAAK;YAClB,OAAO,EAAE,aAAa;YACtB,QAAQ,EAAE,oBAAoB;YAC9B,iBAAiB,EAAE;UACrB,CAAC;UACD,iBAAiB,EAAE;QACrB,CAAC;QACD,QAAQ,EAAE,cAAc;QACxB,iBAAiB,EAAE;MACrB;IACF,CAAC;;IAED;IACA,MAAMC,WAAW,GAAGC,kBAAkB,CAACC,IAAI,CAACC,SAAS,CAACJ,YAAY,CAAC,CAAC;;IAEpE;IACA,MAAMK,gBAAgB,GAAG,IAAIC,eAAe,CAAC;MAC3C,IAAI,EAAE,qBAAqB;MAC3B,QAAQ,EAAE,yBAAyB;MACnC,UAAU,EAAE,QAAQ;MACpB,QAAQ,EAAEL;IACZ,CAAC,CAAC;IAEF,OAAO,GAAGF,OAAO,IAAIM,gBAAgB,CAACE,QAAQ,CAAC,CAAC,EAAE;EACpD,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,GAAG,GAAGX,mBAAmB,CAAC,CAAC;IACjCY,MAAM,CAACC,IAAI,CAACF,GAAG,EAAE,QAAQ,CAAC;EAC5B,CAAC;EAED,oBACEb,OAAA;IAAKgB,KAAK,EAAE;MACVC,eAAe,EAAE,SAAS;MAC1BC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,MAAM;MACfC,MAAM,EAAE,QAAQ;MAChBC,SAAS,EAAE;IACb,CAAE;IAAAC,QAAA,gBACAvB,OAAA;MAAIgB,KAAK,EAAE;QAAEQ,KAAK,EAAE,SAAS;QAAEC,SAAS,EAAE;MAAE,CAAE;MAAAF,QAAA,EAAC;IAE/C;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEL7B,OAAA;MAAGgB,KAAK,EAAE;QAAEQ,KAAK,EAAE,MAAM;QAAEM,YAAY,EAAE;MAAO,CAAE;MAAAP,QAAA,EAAC;IAEnD;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAEJ7B,OAAA;MAAKgB,KAAK,EAAE;QACVC,eAAe,EAAE,MAAM;QACvBG,OAAO,EAAE,MAAM;QACfD,YAAY,EAAE,KAAK;QACnBW,YAAY,EAAE,MAAM;QACpBC,UAAU,EAAE,WAAW;QACvBC,QAAQ,EAAE,MAAM;QAChBR,KAAK,EAAE;MACT,CAAE;MAAAD,QAAA,gBACAvB,OAAA;QAAAuB,QAAA,EAAK;MAAwB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnC7B,OAAA;QAAAuB,QAAA,EAAK;MAAgD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC3D7B,OAAA;QAAAuB,QAAA,EAAK;MAA0B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrC7B,OAAA;QAAAuB,QAAA,EAAK;MAAiC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5C7B,OAAA;QAAAuB,QAAA,EAAK;MAA2C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC,eAEN7B,OAAA;MACEiC,OAAO,EAAErB,kBAAmB;MAC5BI,KAAK,EAAE;QACLC,eAAe,EAAE,SAAS;QAC1BO,KAAK,EAAE,OAAO;QACdN,MAAM,EAAE,MAAM;QACdE,OAAO,EAAE,WAAW;QACpBD,YAAY,EAAE,KAAK;QACnBa,QAAQ,EAAE,MAAM;QAChBE,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAE,MAAM;QAClBC,UAAU,EAAE;MACd,CAAE;MACFC,WAAW,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACvB,KAAK,CAACC,eAAe,GAAG,SAAU;MACtEuB,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACvB,KAAK,CAACC,eAAe,GAAG,SAAU;MAAAM,QAAA,EACtE;IAED;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAET7B,OAAA;MAAKgB,KAAK,EAAE;QACVgB,QAAQ,EAAE,MAAM;QAChBR,KAAK,EAAE,MAAM;QACbC,SAAS,EAAE;MACb,CAAE;MAAAF,QAAA,EAAC;IAEH;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACY,EAAA,GAlLIxC,kBAA4B;AAoLlC,eAAeA,kBAAkB;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}