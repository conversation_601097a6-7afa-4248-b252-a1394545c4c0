/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background: #0d1117;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    '<PERSON>buntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Panel Styles */
.panel {
  background: #161b22;
  border: 1px solid #30363d;
  border-radius: 8px;
  overflow: hidden;
}

.panel-header {
  background: #21262d;
  padding: 12px 16px;
  border-bottom: 1px solid #30363d;
}

.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #f0f6fc;
}

.panel-subtitle {
  margin: 4px 0 0 0;
  font-size: 12px;
  color: #8b949e;
}

.panel-content {
  padding: 16px;
}

/* <PERSON><PERSON> */
.btn {
  background: #238636;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn:hover {
  background: #2ea043;
}

.btn-danger {
  background: #da3633;
}

.btn-danger:hover {
  background: #f85149;
}

/* Status Indicators */
.status-connected {
  color: #3fb950;
}

.status-disconnected {
  color: #f85149;
}

/* Grid Layout */
.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 80px; /* Space for status bar */
}

@media (max-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
}

/* Canvas Styles */
canvas {
  max-width: 100%;
  height: auto;
  border: 1px solid #30363d;
  border-radius: 4px;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #161b22;
}

::-webkit-scrollbar-thumb {
  background: #30363d;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #484f58;
}
