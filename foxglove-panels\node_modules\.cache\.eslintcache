[{"F:\\foxglove\\foxglove-panels\\src\\index.tsx": "1", "F:\\foxglove\\foxglove-panels\\src\\App.tsx": "2", "F:\\foxglove\\foxglove-panels\\src\\components\\RobotDashboard.tsx": "3", "F:\\foxglove\\foxglove-panels\\src\\components\\FoxgloveStudioLink.tsx": "4"}, {"size": 273, "mtime": 1751546645983, "results": "5", "hashOfConfig": "6"}, {"size": 224, "mtime": 1751543788939, "results": "7", "hashOfConfig": "6"}, {"size": 25095, "mtime": 1751546753295, "results": "8", "hashOfConfig": "6"}, {"size": 5215, "mtime": 1751546413667, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1glud09", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "F:\\foxglove\\foxglove-panels\\src\\index.tsx", [], [], "F:\\foxglove\\foxglove-panels\\src\\App.tsx", [], [], "F:\\foxglove\\foxglove-panels\\src\\components\\RobotDashboard.tsx", [], [], "F:\\foxglove\\foxglove-panels\\src\\components\\FoxgloveStudioLink.tsx", [], []]