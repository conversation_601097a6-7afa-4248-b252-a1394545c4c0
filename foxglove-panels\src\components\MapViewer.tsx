import React, { useEffect, useRef } from 'react';

interface MapViewerProps {
  websocketUrl: string;
  robotPosition: { x: number; y: number; z: number };
  isConnected: boolean;
}

const MapViewer: React.FC<MapViewerProps> = ({ websocketUrl, robotPosition, isConnected }) => {
  const mapCanvasRef = useRef<HTMLCanvasElement>(null);

  // Connect to map data
  useEffect(() => {
    const ws = new WebSocket(websocketUrl);

    ws.onopen = () => {
      ws.send(JSON.stringify({
        op: 'subscribe',
        topic: '/map',
        type: 'nav_msgs/OccupancyGrid'
      }));
    };

    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        if (message.topic === '/map' && message.msg && mapCanvasRef.current) {
          const mapMsg = message.msg;
          const canvas = mapCanvasRef.current;
          const ctx = canvas.getContext('2d');

          if (ctx) {
            canvas.width = mapMsg.info.width;
            canvas.height = mapMsg.info.height;
            const imageData = ctx.createImageData(mapMsg.info.width, mapMsg.info.height);

            for (let i = 0; i < mapMsg.data.length; i++) {
              const value = mapMsg.data[i];
              const pixelIndex = i * 4;
              if (value === -1) {
                imageData.data[pixelIndex] = 128;
                imageData.data[pixelIndex + 1] = 128;
                imageData.data[pixelIndex + 2] = 128;
              } else if (value === 0) {
                imageData.data[pixelIndex] = 255;
                imageData.data[pixelIndex + 1] = 255;
                imageData.data[pixelIndex + 2] = 255;
              } else {
                imageData.data[pixelIndex] = 0;
                imageData.data[pixelIndex + 1] = 0;
                imageData.data[pixelIndex + 2] = 0;
              }
              imageData.data[pixelIndex + 3] = 255;
            }
            ctx.putImageData(imageData, 0, 0);
          }
        }
      } catch (error) {
        console.error('Error processing map message:', error);
      }
    };

    return () => ws.close();
  }, [websocketUrl]);

  // Draw robot position on map
  useEffect(() => {
    if (mapCanvasRef.current && isConnected) {
      const canvas = mapCanvasRef.current;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        const robotX = (robotPosition.x * 20) + canvas.width / 2;
        const robotY = canvas.height - ((robotPosition.y * 20) + canvas.height / 2);
        
        ctx.fillStyle = '#ff4444';
        ctx.beginPath();
        ctx.arc(robotX, robotY, 5, 0, 2 * Math.PI);
        ctx.fill();
      }
    }
  }, [robotPosition, isConnected]);

  return (
    <div style={{
      backgroundColor: '#1a1a1a',
      border: '1px solid #333',
      borderRadius: '8px',
      padding: '20px'
    }}>
      <h3 style={{ color: '#9C27B0', marginTop: 0 }}>🗺️ Map Visualization</h3>
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#000',
        borderRadius: '4px',
        border: '1px solid #444',
        minHeight: '200px'
      }}>
        <canvas 
          ref={mapCanvasRef}
          style={{ 
            maxWidth: '100%',
            maxHeight: '200px',
            borderRadius: '4px'
          }}
        />
      </div>
      <div style={{ 
        fontSize: '12px', 
        color: '#aaa', 
        marginTop: '10px',
        textAlign: 'center'
      }}>
        Topic: /map | Robot position: ({robotPosition.x.toFixed(2)}, {robotPosition.y.toFixed(2)})
      </div>
    </div>
  );
};

export default MapViewer;
