import React, { useEffect, useRef, useState } from 'react';
import '../styles/MapViewer.css';

interface MapViewerProps {
  websocketUrl: string;
  robotPosition: { x: number; y: number; z: number };
  isConnected: boolean;
}

const MapViewer: React.FC<MapViewerProps> = ({ websocketUrl, robotPosition, isConnected }) => {
  const mapCanvasRef = useRef<HTMLCanvasElement>(null);
  const [mapData, setMapData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Connect to map data
  useEffect(() => {
    const ws = new WebSocket(websocketUrl);

    ws.onopen = () => {
      ws.send(JSON.stringify({
        op: 'subscribe',
        topic: '/map',
        type: 'nav_msgs/OccupancyGrid'
      }));
    };

    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        if (message.topic === '/map' && message.msg && mapCanvasRef.current) {
          const mapMsg = message.msg;
          setMapData(mapMsg);
          setIsLoading(false);

          const canvas = mapCanvasRef.current;
          const ctx = canvas.getContext('2d');

          if (ctx) {
            canvas.width = mapMsg.info.width;
            canvas.height = mapMsg.info.height;
            const imageData = ctx.createImageData(mapMsg.info.width, mapMsg.info.height);

            for (let i = 0; i < mapMsg.data.length; i++) {
              const value = mapMsg.data[i];
              const pixelIndex = i * 4;
              if (value === -1) {
                imageData.data[pixelIndex] = 128;
                imageData.data[pixelIndex + 1] = 128;
                imageData.data[pixelIndex + 2] = 128;
              } else if (value === 0) {
                imageData.data[pixelIndex] = 255;
                imageData.data[pixelIndex + 1] = 255;
                imageData.data[pixelIndex + 2] = 255;
              } else {
                imageData.data[pixelIndex] = 0;
                imageData.data[pixelIndex + 1] = 0;
                imageData.data[pixelIndex + 2] = 0;
              }
              imageData.data[pixelIndex + 3] = 255;
            }
            ctx.putImageData(imageData, 0, 0);
          }
        }
      } catch (error) {
        console.error('Error processing map message:', error);
      }
    };

    return () => ws.close();
  }, [websocketUrl]);

  // Draw robot position on map
  useEffect(() => {
    if (mapCanvasRef.current && isConnected) {
      const canvas = mapCanvasRef.current;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        const robotX = (robotPosition.x * 20) + canvas.width / 2;
        const robotY = canvas.height - ((robotPosition.y * 20) + canvas.height / 2);
        
        ctx.fillStyle = '#ff4444';
        ctx.beginPath();
        ctx.arc(robotX, robotY, 5, 0, 2 * Math.PI);
        ctx.fill();
      }
    }
  }, [robotPosition, isConnected]);

  return (
    <div className="map-viewer-container">
      <div className="map-viewer-header">
        <h3 className="map-viewer-title">
          <span className="map-viewer-icon">🗺️</span>
          Map Visualization
        </h3>
      </div>

      <div className="map-canvas-container">
        {isLoading ? (
          <div className="map-loading">
            <div className="map-loading-spinner"></div>
            <div>Loading map data...</div>
          </div>
        ) : (
          <>
            <canvas
              ref={mapCanvasRef}
              className="map-canvas"
            />
            {isConnected && (
              <div
                className="robot-position-overlay"
                style={{
                  left: `${(robotPosition.x * 20) + 50}%`,
                  top: `${50 - (robotPosition.y * 20)}%`
                }}
              />
            )}
          </>
        )}
      </div>

      <div className="map-status">
        <div className="map-info">
          <span className="map-topic-info">Topic: /map</span>
          <span className="map-robot-position">
            Robot: ({robotPosition.x.toFixed(2)}, {robotPosition.y.toFixed(2)})
          </span>
        </div>
        <div className={`map-connection-status ${isConnected ? 'map-connected' : 'map-disconnected'}`}>
          {isConnected ? 'Connected' : 'Disconnected'}
        </div>
      </div>

      {mapData && (
        <div className="map-controls">
          <button className="map-control-button">Reset View</button>
          <button className="map-control-button">Center Robot</button>
          <button className="map-control-button">Zoom Fit</button>
        </div>
      )}
    </div>
  );
};

export default MapViewer;
