/* Position Display Component Styles */
.position-display-container {
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 20px;
}

.position-display-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.position-display-title {
  color: #2196F3;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.position-display-icon {
  margin-right: 8px;
  font-size: 20px;
}

.position-data-container {
  background-color: #000;
  border-radius: 4px;
  border: 1px solid #444;
  padding: 15px;
}

.position-section {
  margin-bottom: 20px;
}

.position-section:last-child {
  margin-bottom: 0;
}

.position-section-title {
  color: #2196F3;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.position-data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.position-data-item {
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 4px;
  padding: 10px;
  text-align: center;
}

.position-data-label {
  color: #aaa;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.position-data-value {
  color: #fff;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  font-weight: 600;
}

.position-coordinate {
  color: #2196F3;
}

.position-orientation {
  color: #FF9800;
}

.position-velocity {
  color: #4CAF50;
}

/* Status Indicators */
.position-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #333;
}

.position-timestamp {
  color: #666;
  font-size: 11px;
  font-family: 'Courier New', monospace;
}

.position-connection-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.position-connected {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid #4CAF50;
}

.position-disconnected {
  background-color: rgba(244, 67, 54, 0.2);
  color: #F44336;
  border: 1px solid #F44336;
}

/* Loading State */
.position-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #666;
}

.position-loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #333;
  border-top: 3px solid #2196F3;
  border-radius: 50%;
  animation: positionSpin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes positionSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Precision Indicators */
.position-precision-high {
  border-left: 3px solid #4CAF50;
}

.position-precision-medium {
  border-left: 3px solid #FF9800;
}

.position-precision-low {
  border-left: 3px solid #F44336;
}

/* Responsive Design */
@media (max-width: 768px) {
  .position-display-container {
    padding: 15px;
  }
  
  .position-data-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
  
  .position-data-item {
    padding: 8px;
  }
  
  .position-data-value {
    font-size: 12px;
  }
  
  .position-status {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}

/* Animation for value updates */
.position-data-value.updated {
  animation: valueUpdate 0.3s ease-in-out;
}

@keyframes valueUpdate {
  0% { 
    background-color: rgba(33, 150, 243, 0.3);
    transform: scale(1);
  }
  50% { 
    background-color: rgba(33, 150, 243, 0.1);
    transform: scale(1.05);
  }
  100% { 
    background-color: transparent;
    transform: scale(1);
  }
}
