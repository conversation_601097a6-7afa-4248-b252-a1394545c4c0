/* Sensor Data Display Component Styles */
.sensor-display-container {
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 20px;
}

.sensor-display-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.sensor-display-title {
  color: #FF5722;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.sensor-display-icon {
  margin-right: 8px;
  font-size: 20px;
}

.sensor-data-container {
  background-color: #000;
  border-radius: 4px;
  border: 1px solid #444;
  padding: 15px;
}

.sensor-visualization {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  margin-bottom: 15px;
  background-color: #0a0a0a;
  border-radius: 4px;
  border: 1px solid #333;
  position: relative;
  overflow: hidden;
}

.sensor-canvas {
  max-width: 100%;
  max-height: 180px;
}

.sensor-polar-grid {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 180px;
  height: 180px;
  border: 1px solid #333;
  border-radius: 50%;
}

.sensor-polar-grid::before,
.sensor-polar-grid::after {
  content: '';
  position: absolute;
  background-color: #333;
}

.sensor-polar-grid::before {
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  transform: translateY(-50%);
}

.sensor-polar-grid::after {
  left: 50%;
  top: 0;
  bottom: 0;
  width: 1px;
  transform: translateX(-50%);
}

.sensor-data-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
}

.sensor-stat-item {
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 4px;
  padding: 8px;
  text-align: center;
}

.sensor-stat-label {
  color: #aaa;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.sensor-stat-value {
  color: #fff;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  font-weight: 600;
}

.sensor-range-min {
  color: #4CAF50;
}

.sensor-range-max {
  color: #F44336;
}

.sensor-range-avg {
  color: #FF9800;
}

.sensor-point-count {
  color: #2196F3;
}

/* Laser Scan Visualization */
.laser-scan-points {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.laser-point {
  position: absolute;
  width: 2px;
  height: 2px;
  background-color: #FF5722;
  border-radius: 50%;
  box-shadow: 0 0 3px rgba(255, 87, 34, 0.6);
}

.laser-point.close {
  background-color: #F44336;
  box-shadow: 0 0 4px rgba(244, 67, 54, 0.8);
}

.laser-point.far {
  background-color: #4CAF50;
  box-shadow: 0 0 2px rgba(76, 175, 80, 0.4);
}

/* Status and Controls */
.sensor-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #333;
}

.sensor-topic-info {
  color: #FF5722;
  font-size: 11px;
  font-family: 'Courier New', monospace;
}

.sensor-connection-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.sensor-connected {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid #4CAF50;
}

.sensor-disconnected {
  background-color: rgba(244, 67, 54, 0.2);
  color: #F44336;
  border: 1px solid #F44336;
}

/* Loading State */
.sensor-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #666;
}

.sensor-loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #333;
  border-top: 3px solid #FF5722;
  border-radius: 50%;
  animation: sensorSpin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes sensorSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .sensor-display-container {
    padding: 15px;
  }
  
  .sensor-visualization {
    min-height: 150px;
  }
  
  .sensor-polar-grid {
    width: 140px;
    height: 140px;
  }
  
  .sensor-data-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
  
  .sensor-stat-item {
    padding: 6px;
  }
  
  .sensor-status {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}

/* Animation for new data */
.sensor-data-update {
  animation: sensorDataPulse 0.5s ease-in-out;
}

@keyframes sensorDataPulse {
  0% { 
    border-color: #FF5722;
    box-shadow: 0 0 10px rgba(255, 87, 34, 0.3);
  }
  50% { 
    border-color: #FF7043;
    box-shadow: 0 0 15px rgba(255, 87, 34, 0.5);
  }
  100% { 
    border-color: #444;
    box-shadow: none;
  }
}
