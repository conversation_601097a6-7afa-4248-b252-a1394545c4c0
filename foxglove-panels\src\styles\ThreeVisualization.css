/* Three.js Visualization Component Styles */
.three-visualization-container {
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 20px;
}

.three-visualization-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.three-visualization-title {
  color: #673AB7;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.three-visualization-icon {
  margin-right: 8px;
  font-size: 20px;
}

.three-controls {
  display: flex;
  gap: 8px;
}

.three-control-button {
  background-color: #333;
  border: 1px solid #555;
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.three-control-button:hover {
  background-color: #444;
}

.three-control-button.active {
  background-color: #673AB7;
  border-color: #673AB7;
}

.three-canvas-container {
  background-color: #000;
  border-radius: 4px;
  border: 1px solid #444;
  position: relative;
  overflow: hidden;
  min-height: 400px;
}

.three-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

.three-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #666;
}

.three-loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #333;
  border-top: 4px solid #673AB7;
  border-radius: 50%;
  animation: threeSpin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes threeSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Overlay Controls */
.three-overlay-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.three-overlay-button {
  background-color: rgba(0, 0, 0, 0.7);
  border: 1px solid #555;
  color: #fff;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
}

.three-overlay-button:hover {
  background-color: rgba(0, 0, 0, 0.9);
  border-color: #673AB7;
}

/* Camera Controls Info */
.three-camera-info {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  border: 1px solid #555;
  border-radius: 4px;
  padding: 8px 12px;
  color: #aaa;
  font-size: 11px;
  font-family: 'Courier New', monospace;
  backdrop-filter: blur(4px);
}

.three-camera-position {
  margin-bottom: 4px;
}

.three-camera-target {
  color: #673AB7;
}

/* Performance Stats */
.three-performance-stats {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  border: 1px solid #555;
  border-radius: 4px;
  padding: 8px 12px;
  color: #aaa;
  font-size: 11px;
  font-family: 'Courier New', monospace;
  backdrop-filter: blur(4px);
}

.three-fps {
  color: #4CAF50;
  margin-bottom: 4px;
}

.three-triangles {
  color: #FF9800;
}

/* Status Bar */
.three-status-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #333;
}

.three-render-info {
  display: flex;
  gap: 20px;
  font-size: 11px;
  color: #aaa;
}

.three-render-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.three-render-value {
  color: #fff;
  font-family: 'Courier New', monospace;
  font-weight: 600;
  margin-bottom: 2px;
}

.three-render-label {
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.three-connection-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.three-connected {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid #4CAF50;
}

.three-disconnected {
  background-color: rgba(244, 67, 54, 0.2);
  color: #F44336;
  border: 1px solid #F44336;
}

/* Scene Objects */
.three-scene-objects {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  border: 1px solid #555;
  border-radius: 4px;
  padding: 8px 12px;
  color: #aaa;
  font-size: 11px;
  backdrop-filter: blur(4px);
  max-width: 200px;
}

.three-object-count {
  color: #673AB7;
  font-weight: 600;
  margin-bottom: 4px;
}

.three-object-list {
  font-size: 10px;
  line-height: 1.3;
}

/* Responsive Design */
@media (max-width: 768px) {
  .three-visualization-container {
    padding: 15px;
  }
  
  .three-canvas-container {
    min-height: 300px;
  }
  
  .three-overlay-controls {
    top: 5px;
    right: 5px;
    gap: 4px;
  }
  
  .three-overlay-button {
    padding: 6px;
    font-size: 11px;
  }
  
  .three-camera-info,
  .three-performance-stats,
  .three-scene-objects {
    font-size: 10px;
    padding: 6px 8px;
  }
  
  .three-status-bar {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
  
  .three-render-info {
    justify-content: center;
  }
}

/* Animation for scene updates */
.three-scene-update {
  animation: threeSceneUpdate 0.3s ease-in-out;
}

@keyframes threeSceneUpdate {
  0% { 
    border-color: #673AB7;
    box-shadow: 0 0 10px rgba(103, 58, 183, 0.3);
  }
  50% { 
    border-color: #7E57C2;
    box-shadow: 0 0 15px rgba(103, 58, 183, 0.5);
  }
  100% { 
    border-color: #444;
    box-shadow: none;
  }
}
